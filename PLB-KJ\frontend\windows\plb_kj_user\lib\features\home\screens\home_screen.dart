import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/services/customer_auth_service.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('PLB-KJ 跨境电商'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          Consumer<CustomerAuthService>(
            builder: (context, authService, child) {
              return PopupMenuButton<String>(
                icon: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircleAvatar(
                      radius: 16,
                      backgroundColor: Colors.white,
                      child: Text(
                        authService.currentCustomer?.displayName.substring(0, 1).toUpperCase() ?? 'U',
                        style: TextStyle(
                          color: Theme.of(context).primaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      authService.currentCustomer?.displayName ?? '用户',
                      style: const TextStyle(fontSize: 14),
                    ),
                    const Icon(Icons.arrow_drop_down),
                  ],
                ),
                onSelected: (value) async {
                  switch (value) {
                    case 'profile':
                      // TODO: 打开个人资料页面
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('个人资料功能开发中...')),
                      );
                      break;
                    case 'orders':
                      // TODO: 打开订单页面
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('我的订单功能开发中...')),
                      );
                      break;
                    case 'favorites':
                      // TODO: 打开收藏页面
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('我的收藏功能开发中...')),
                      );
                      break;
                    case 'settings':
                      // TODO: 打开设置页面
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('设置功能开发中...')),
                      );
                      break;
                    case 'logout':
                      await authService.logout();
                      break;
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'profile',
                    child: ListTile(
                      leading: Icon(Icons.person),
                      title: Text('个人资料'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'orders',
                    child: ListTile(
                      leading: Icon(Icons.shopping_bag),
                      title: Text('我的订单'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'favorites',
                    child: ListTile(
                      leading: Icon(Icons.favorite),
                      title: Text('我的收藏'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'settings',
                    child: ListTile(
                      leading: Icon(Icons.settings),
                      title: Text('设置'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  const PopupMenuDivider(),
                  const PopupMenuItem(
                    value: 'logout',
                    child: ListTile(
                      leading: Icon(Icons.logout, color: Colors.red),
                      title: Text('退出登录', style: TextStyle(color: Colors.red)),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                ],
              );
            },
          ),
          const SizedBox(width: 16),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Consumer<CustomerAuthService>(
              builder: (context, authService, child) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '欢迎回来，${authService.currentCustomer?.displayName ?? '用户'}！',
                      style: const TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '邮箱：${authService.currentCustomer?.email ?? '未知'}',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                );
              },
            ),
            const SizedBox(height: 8),
            Text(
              '选择下方功能开始购物',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 32),

            // 功能模块网格
            Expanded(
              child: GridView.count(
                crossAxisCount: 3,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                children: [
                  _buildFunctionCard(
                    context,
                    icon: Icons.shopping_cart,
                    title: '商品浏览',
                    subtitle: '浏览所有商品',
                    color: Colors.blue,
                    onTap: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('商品浏览功能开发中...')),
                      );
                    },
                  ),
                  _buildFunctionCard(
                    context,
                    icon: Icons.category,
                    title: '商品分类',
                    subtitle: '按分类查看商品',
                    color: Colors.green,
                    onTap: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('商品分类功能开发中...')),
                      );
                    },
                  ),
                  _buildFunctionCard(
                    context,
                    icon: Icons.search,
                    title: '搜索商品',
                    subtitle: '搜索您需要的商品',
                    color: Colors.orange,
                    onTap: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('搜索功能开发中...')),
                      );
                    },
                  ),
                  _buildFunctionCard(
                    context,
                    icon: Icons.shopping_bag,
                    title: '购物车',
                    subtitle: '查看购物车',
                    color: Colors.purple,
                    onTap: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('购物车功能开发中...')),
                      );
                    },
                  ),
                  _buildFunctionCard(
                    context,
                    icon: Icons.favorite,
                    title: '我的收藏',
                    subtitle: '查看收藏的商品',
                    color: Colors.red,
                    onTap: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('收藏功能开发中...')),
                      );
                    },
                  ),
                  _buildFunctionCard(
                    context,
                    icon: Icons.support_agent,
                    title: '客服咨询',
                    subtitle: '在线客服帮助',
                    color: Colors.teal,
                    onTap: () => Navigator.pushNamed(context, '/chat'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFunctionCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 4,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  size: 32,
                  color: color,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
