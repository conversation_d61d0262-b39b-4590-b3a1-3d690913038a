<?php

// 测试管理员登录API

$url = 'http://localhost:8080/api/admin/login';
$data = [
    'username' => 'admin',
    'password' => 'password'
];

$options = [
    'http' => [
        'header' => "Content-type: application/json\r\n",
        'method' => 'POST',
        'content' => json_encode($data)
    ]
];

$context = stream_context_create($options);
$result = file_get_contents($url, false, $context);

if ($result === FALSE) {
    echo "请求失败\n";
} else {
    echo "响应: " . $result . "\n";
}

// 解析响应头
if (isset($http_response_header)) {
    echo "\n响应头:\n";
    foreach ($http_response_header as $header) {
        echo $header . "\n";
    }
}
