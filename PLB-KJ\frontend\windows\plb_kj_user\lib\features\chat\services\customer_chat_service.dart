import 'dart:convert';
import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../models/chat_models.dart';

/// 用户端聊天服务（客户端）
class CustomerChatService extends ChangeNotifier {
  static const String _baseUrl = 'http://localhost/PLB-KJ/backend/public/api';
  
  bool _isConnected = false;
  String? _authToken;
  
  List<ChatSession> _sessions = [];
  Map<String, List<ChatMessage>> _messages = {};
  Timer? _refreshTimer;
  
  // Getters
  bool get isConnected => _isConnected;
  List<ChatSession> get sessions => _sessions;
  Map<String, List<ChatMessage>> get messages => _messages;
  
  // 获取当前活跃会话
  ChatSession? get activeSession => 
      _sessions.where((s) => s.status == 'waiting' || s.status == 'active')
          .isNotEmpty ? _sessions.first : null;
  
  /// 初始化用户端聊天服务
  Future<void> initialize() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('auth_token');
    
    if (token != null) {
      _authToken = token;
      _isConnected = true;
      
      // 加载会话列表
      await loadSessions();
      
      // 启动定时刷新（每30秒）
      _startRefreshTimer();
      
      notifyListeners();
    }
  }
  
  /// 启动定时刷新
  void _startRefreshTimer() {
    _refreshTimer?.cancel();
    _refreshTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      loadSessions();
      // 如果有活跃会话，刷新消息
      if (activeSession != null) {
        getSessionMessages(activeSession!.id);
      }
    });
  }
  
  /// 获取请求头
  Map<String, String> _getHeaders() {
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $_authToken',
    };
  }
  
  /// 加载会话列表
  Future<void> loadSessions() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/chat/sessions'),
        headers: _getHeaders(),
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          _sessions = (data['data']['sessions'] as List)
              .map((json) => ChatSession.fromJson(json))
              .toList();
          notifyListeners();
        }
      }
    } catch (e) {
      print('加载会话列表失败: $e');
    }
  }
  
  /// 创建新会话
  Future<ChatSession?> createSession({
    String title = '客服咨询',
    String category = 'general',
    String priority = 'normal',
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/chat/sessions'),
        headers: _getHeaders(),
        body: json.encode({
          'title': title,
          'category': category,
          'priority': priority,
        }),
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          final session = ChatSession.fromJson(data['data']['session']);
          _sessions.insert(0, session);
          notifyListeners();
          return session;
        }
      }
    } catch (e) {
      print('创建会话失败: $e');
    }
    return null;
  }
  
  /// 获取会话消息
  Future<List<ChatMessage>> getSessionMessages(int sessionId) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/chat/sessions/$sessionId/messages'),
        headers: _getHeaders(),
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          final messages = (data['data']['messages'] as List)
              .map((json) => ChatMessage.fromJson(json))
              .toList();
          
          _messages[sessionId.toString()] = messages;
          notifyListeners();
          
          return messages;
        }
      }
    } catch (e) {
      print('获取消息失败: $e');
    }
    
    return [];
  }
  
  /// 发送消息（客户发送）
  Future<ChatMessage?> sendMessage({
    required int sessionId,
    required String content,
    String messageType = 'text',
    int? replyToMessageId,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/chat/sessions/$sessionId/messages'),
        headers: _getHeaders(),
        body: json.encode({
          'content': content,
          'message_type': messageType,
          'reply_to_message_id': replyToMessageId,
        }),
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          final message = ChatMessage.fromJson(data['data']['message']);
          
          // 添加到本地消息列表
          final sessionKey = sessionId.toString();
          if (!_messages.containsKey(sessionKey)) {
            _messages[sessionKey] = [];
          }
          _messages[sessionKey]!.add(message);
          
          // 更新会话的最后消息时间
          _updateSessionLastMessage(sessionId);
          
          notifyListeners();
          return message;
        }
      }
    } catch (e) {
      print('发送消息失败: $e');
    }
    return null;
  }
  
  /// 关闭会话并评价
  Future<bool> closeSessionWithRating({
    required int sessionId,
    required int rating,
    String? feedback,
  }) async {
    try {
      final response = await http.put(
        Uri.parse('$_baseUrl/chat/sessions/$sessionId/close'),
        headers: _getHeaders(),
        body: json.encode({
          'rating': rating,
          'feedback': feedback,
        }),
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          // 更新本地会话状态
          final sessionIndex = _sessions.indexWhere((s) => s.id == sessionId);
          if (sessionIndex != -1) {
            _sessions[sessionIndex] = _sessions[sessionIndex].copyWith(
              status: 'closed',
              endedAt: DateTime.now(),
              customerSatisfactionRating: rating,
              customerFeedback: feedback,
            );
            notifyListeners();
          }
          return true;
        }
      }
    } catch (e) {
      print('关闭会话失败: $e');
    }
    return false;
  }
  
  /// 检查是否有活跃会话
  bool hasActiveSession() {
    return activeSession != null;
  }
  
  /// 获取未读消息数量
  int getUnreadMessageCount() {
    int count = 0;
    for (final session in _sessions) {
      count += session.unreadCount;
    }
    return count;
  }
  
  /// 刷新数据
  Future<void> refresh() async {
    await loadSessions();
    
    // 刷新活跃会话的消息
    if (activeSession != null) {
      await getSessionMessages(activeSession!.id);
    }
  }
  
  /// 更新会话最后消息时间
  void _updateSessionLastMessage(int sessionId) {
    final sessionIndex = _sessions.indexWhere((s) => s.id == sessionId);
    if (sessionIndex != -1) {
      _sessions[sessionIndex] = _sessions[sessionIndex].copyWith(
        lastMessageAt: DateTime.now(),
      );
    }
  }
  
  /// 断开连接
  void disconnect() {
    _refreshTimer?.cancel();
    _isConnected = false;
    _authToken = null;
    _sessions.clear();
    _messages.clear();
    notifyListeners();
  }
  
  @override
  void dispose() {
    disconnect();
    super.dispose();
  }
}
