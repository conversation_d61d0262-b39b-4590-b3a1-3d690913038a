-- PLB-KJ 数据库设置 - MySQL 5.7 兼容版本
-- 专门解决MySQL 5.7的兼容性问题

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 1. 创建系统设置表
CREATE TABLE IF NOT EXISTS `plb_kj_system_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL COMMENT '设置键名',
  `setting_value` text COMMENT '设置值',
  `setting_type` enum('string','number','boolean','json') NOT NULL DEFAULT 'string' COMMENT '设置类型',
  `category` varchar(50) NOT NULL DEFAULT 'general' COMMENT '设置分类',
  `description` varchar(255) DEFAULT NULL COMMENT '设置描述',
  `is_public` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否公开',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`),
  KEY `idx_category` (`category`),
  KEY `idx_is_public` (`is_public`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统设置表';

-- 2. 创建聊天会话表
CREATE TABLE IF NOT EXISTS `plb_kj_chat_sessions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` varchar(36) NOT NULL COMMENT '会话唯一标识符',
  `customer_id` int(11) NOT NULL COMMENT '客户ID',
  `admin_user_id` int(11) DEFAULT NULL COMMENT '客服ID',
  `title` varchar(255) NOT NULL DEFAULT '客服咨询' COMMENT '会话标题',
  `status` enum('waiting','active','closed') NOT NULL DEFAULT 'waiting' COMMENT '会话状态',
  `priority` enum('low','normal','high','urgent') NOT NULL DEFAULT 'normal' COMMENT '优先级',
  `category` enum('general','technical','billing','complaint') NOT NULL DEFAULT 'general' COMMENT '分类',
  `started_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
  `ended_at` timestamp NULL DEFAULT NULL COMMENT '结束时间',
  `last_message_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后消息时间',
  `customer_satisfaction_rating` tinyint(1) DEFAULT NULL COMMENT '客户满意度评分(1-5)',
  `customer_feedback` text COMMENT '客户反馈',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `session_id` (`session_id`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_admin_user_id` (`admin_user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_priority` (`priority`),
  KEY `idx_started_at` (`started_at`),
  KEY `idx_last_message_at` (`last_message_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天会话表';

-- 3. 创建聊天消息表
CREATE TABLE IF NOT EXISTS `plb_kj_chat_messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` int(11) NOT NULL COMMENT '会话ID',
  `sender_type` enum('customer','admin') NOT NULL COMMENT '发送者类型',
  `sender_id` int(11) NOT NULL COMMENT '发送者ID',
  `message_type` enum('text','image','file','audio') NOT NULL DEFAULT 'text' COMMENT '消息类型',
  `content` text NOT NULL COMMENT '消息内容',
  `reply_to_message_id` int(11) DEFAULT NULL COMMENT '回复的消息ID',
  `is_read` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已读',
  `read_at` timestamp NULL DEFAULT NULL COMMENT '已读时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_sender` (`sender_type`, `sender_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_is_read` (`is_read`),
  KEY `idx_reply_to_message_id` (`reply_to_message_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天消息表';

-- 4. 创建在线状态表
CREATE TABLE IF NOT EXISTS `plb_kj_online_status` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_type` enum('customer','admin') NOT NULL COMMENT '用户类型',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `socket_id` varchar(255) NOT NULL COMMENT 'Socket连接ID',
  `last_seen` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后在线时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user` (`user_type`, `user_id`),
  KEY `idx_socket_id` (`socket_id`),
  KEY `idx_last_seen` (`last_seen`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='在线状态表';

-- 5. 创建快捷回复模板表
CREATE TABLE IF NOT EXISTS `plb_kj_chat_quick_replies` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category` varchar(50) NOT NULL COMMENT '分类',
  `title` varchar(100) NOT NULL COMMENT '标题',
  `content` text NOT NULL COMMENT '内容',
  `sort_order` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `created_by` int(11) NOT NULL COMMENT '创建者ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_category` (`category`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='快捷回复模板表';

-- 6. MySQL 5.7 兼容的字段添加方式
-- 检查并添加 is_online 字段到 plb_kj_customers 表
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
   WHERE table_name = 'plb_kj_customers' 
   AND column_name = 'is_online' 
   AND table_schema = DATABASE()) = 0,
  'ALTER TABLE plb_kj_customers ADD COLUMN is_online tinyint(1) NOT NULL DEFAULT 0 COMMENT "是否在线"',
  'SELECT "Column is_online already exists in plb_kj_customers" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加 last_login_at 字段到 plb_kj_customers 表
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
   WHERE table_name = 'plb_kj_customers' 
   AND column_name = 'last_login_at' 
   AND table_schema = DATABASE()) = 0,
  'ALTER TABLE plb_kj_customers ADD COLUMN last_login_at timestamp NULL DEFAULT NULL COMMENT "最后登录时间"',
  'SELECT "Column last_login_at already exists in plb_kj_customers" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加 is_online 字段到 plb_kj_admin_users 表
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
   WHERE table_name = 'plb_kj_admin_users' 
   AND column_name = 'is_online' 
   AND table_schema = DATABASE()) = 0,
  'ALTER TABLE plb_kj_admin_users ADD COLUMN is_online tinyint(1) NOT NULL DEFAULT 0 COMMENT "是否在线"',
  'SELECT "Column is_online already exists in plb_kj_admin_users" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加 last_login_at 字段到 plb_kj_admin_users 表
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
   WHERE table_name = 'plb_kj_admin_users' 
   AND column_name = 'last_login_at' 
   AND table_schema = DATABASE()) = 0,
  'ALTER TABLE plb_kj_admin_users ADD COLUMN last_login_at timestamp NULL DEFAULT NULL COMMENT "最后登录时间"',
  'SELECT "Column last_login_at already exists in plb_kj_admin_users" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 7. 插入系统设置数据
INSERT IGNORE INTO `plb_kj_system_settings` (`setting_key`, `setting_value`, `setting_type`, `category`, `description`, `is_public`) VALUES
('site_name', 'PLB-KJ跨境电商管理系统', 'string', 'general', '网站名称', 1),
('site_email', '<EMAIL>', 'string', 'general', '网站邮箱', 0),
('currency_default', 'USD', 'string', 'general', '默认货币', 1),
('tax_rate', '0.00', 'number', 'general', '默认税率', 1),
('chat_enabled', '1', 'boolean', 'chat', '是否启用聊天功能', 1),
('chat_max_sessions_per_agent', '5', 'number', 'chat', '每个客服最大并发会话数', 0),
('chat_auto_assign', '1', 'boolean', 'chat', '是否自动分配客服', 0),
('chat_session_timeout', '1800', 'number', 'chat', '会话超时时间(秒)', 0),
('chat_file_upload_enabled', '1', 'boolean', 'chat', '是否允许文件上传', 0),
('chat_file_max_size', '10485760', 'number', 'chat', '文件上传最大大小(字节)', 0);

-- 8. 插入快捷回复模板数据
INSERT IGNORE INTO `plb_kj_chat_quick_replies` (`category`, `title`, `content`, `sort_order`, `created_by`) VALUES
('greeting', '欢迎语', '您好！欢迎来到我们的在线客服，我是您的专属客服，有什么可以帮助您的吗？', 1, 1),
('common', '稍等回复', '好的，请稍等，我马上为您查询处理。', 2, 1),
('common', '感谢回复', '感谢您的耐心等待，如果还有其他问题，请随时联系我们。', 3, 1),
('technical', '技术支持', '我来为您解决技术问题，请详细描述您遇到的情况。', 4, 1),
('billing', '账单查询', '我来帮您查看账单详情，请提供您的订单号或账户信息。', 5, 1),
('closing', '结束语', '感谢您的咨询，如果还有其他问题，随时联系我们。祝您购物愉快！', 6, 1);

-- 9. 插入示例聊天数据
INSERT IGNORE INTO `plb_kj_chat_sessions` (`session_id`, `customer_id`, `admin_user_id`, `title`, `status`, `priority`, `category`, `started_at`, `last_message_at`) VALUES
('550e8400-e29b-41d4-a716-446655440001', 1, 1, '产品咨询', 'active', 'normal', 'general', NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440002', 1, NULL, '技术支持', 'waiting', 'high', 'technical', NOW(), NOW());

INSERT IGNORE INTO `plb_kj_chat_messages` (`session_id`, `sender_type`, `sender_id`, `message_type`, `content`, `is_read`) VALUES
(1, 'customer', 1, 'text', '你好，我想咨询一下产品信息', 1),
(1, 'admin', 1, 'text', '您好！很高兴为您服务，请问您想了解哪款产品呢？', 1),
(2, 'customer', 1, 'text', '我的系统有问题，需要技术支持', 0);

-- 10. 创建视图（MySQL 5.7兼容）
CREATE OR REPLACE VIEW `plb_kj_chat_session_details` AS
SELECT 
    s.id,
    s.session_id,
    s.customer_id,
    s.admin_user_id,
    s.title,
    s.status,
    s.priority,
    s.category,
    s.started_at,
    s.ended_at,
    s.last_message_at,
    s.customer_satisfaction_rating,
    s.customer_feedback,
    COALESCE(c.first_name, '') as customer_first_name,
    COALESCE(c.last_name, '') as customer_last_name,
    COALESCE(c.email, '') as customer_email,
    COALESCE(c.avatar, '') as customer_avatar,
    COALESCE(c.is_online, 0) as customer_is_online,
    COALESCE(a.first_name, '') as admin_first_name,
    COALESCE(a.last_name, '') as admin_last_name,
    COALESCE(a.email, '') as admin_email,
    COALESCE(a.avatar, '') as admin_avatar,
    COALESCE(a.is_online, 0) as admin_is_online,
    (SELECT COUNT(*) FROM plb_kj_chat_messages m WHERE m.session_id = s.id AND m.is_read = 0 AND m.sender_type = 'customer') as unread_count
FROM plb_kj_chat_sessions s
LEFT JOIN plb_kj_customers c ON s.customer_id = c.id
LEFT JOIN plb_kj_admin_users a ON s.admin_user_id = a.id;

-- 11. 创建触发器
DROP TRIGGER IF EXISTS update_last_message_time;
DELIMITER //
CREATE TRIGGER update_last_message_time 
AFTER INSERT ON plb_kj_chat_messages
FOR EACH ROW
BEGIN
    UPDATE plb_kj_chat_sessions 
    SET last_message_at = NEW.created_at 
    WHERE id = NEW.session_id;
END //
DELIMITER ;

SET FOREIGN_KEY_CHECKS = 1;

-- 验证安装结果
SELECT 'Setup completed successfully!' as status;
SELECT COUNT(*) as total_settings FROM plb_kj_system_settings;
SELECT COUNT(*) as total_sessions FROM plb_kj_chat_sessions;
SELECT COUNT(*) as total_messages FROM plb_kj_chat_messages;
SELECT COUNT(*) as total_quick_replies FROM plb_kj_chat_quick_replies;
