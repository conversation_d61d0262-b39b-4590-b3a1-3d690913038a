import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/api_constants.dart';
import '../models/admin_user.dart';
import '../models/api_response.dart';
import 'api_service.dart';

/// 认证服务
class AuthService extends ChangeNotifier {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final ApiService _apiService = ApiService();
  
  AdminUser? _currentUser;
  bool _isAuthenticated = false;
  bool _isLoading = false;

  /// 当前用户
  AdminUser? get currentUser => _currentUser;

  /// 是否已认证
  bool get isAuthenticated => _isAuthenticated;

  /// 是否正在加载
  bool get isLoading => _isLoading;

  /// 初始化认证服务
  Future<void> initialize() async {
    await _loadUserFromStorage();
  }

  /// 管理员登录
  Future<ApiResponse<AdminLoginResponse>> login({
    required String username,
    required String password,
    String? captcha,
  }) async {
    _setLoading(true);
    
    try {
      final request = AdminLoginRequest(
        username: username,
        password: password,
        captcha: captcha,
      );

      final response = await _apiService.post<AdminLoginResponse>(
        ApiConstants.login,
        data: request.toJson(),
        fromJson: (json) => AdminLoginResponse.fromJson(json),
      );

      if (response.isSuccess && response.data != null) {
        final loginData = response.data!;
        
        // 保存token
        await _apiService.setAuthToken(loginData.token);
        
        // 保存用户信息
        _currentUser = loginData.admin;
        _isAuthenticated = true;
        
        // 持久化用户信息
        await _saveUserToStorage();
        
        notifyListeners();
      }

      return response;
    } finally {
      _setLoading(false);
    }
  }

  /// 退出登录
  Future<void> logout() async {
    _setLoading(true);
    
    try {
      // 清除token
      await _apiService.clearAuthToken();
      
      // 清除用户信息
      _currentUser = null;
      _isAuthenticated = false;
      
      // 清除本地存储
      await _clearUserFromStorage();
      
      notifyListeners();
    } finally {
      _setLoading(false);
    }
  }

  /// 验证token有效性
  Future<bool> validateToken() async {
    if (!_apiService.isAuthenticated) {
      return false;
    }

    try {
      final response = await _apiService.get(ApiConstants.validateToken);
      
      if (response.isSuccess) {
        return true;
      } else {
        // Token无效，清除认证状态
        await logout();
        return false;
      }
    } catch (e) {
      // 验证失败，清除认证状态
      await logout();
      return false;
    }
  }

  /// 获取验证码
  Future<ApiResponse<String>> getCaptcha() async {
    final response = await _apiService.get<String>(
      ApiConstants.captcha,
      fromJson: (json) => json['image'] as String,
    );
    
    return response;
  }

  /// 更新用户信息
  void updateUser(AdminUser user) {
    _currentUser = user;
    _saveUserToStorage();
    notifyListeners();
  }

  /// 设置加载状态
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// 从本地存储加载用户信息
  Future<void> _loadUserFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString('current_user');
      
      if (userJson != null && _apiService.isAuthenticated) {
        final userMap = Map<String, dynamic>.from(
          Uri.splitQueryString(userJson),
        );
        _currentUser = AdminUser.fromJson(userMap);
        _isAuthenticated = true;
        notifyListeners();
      }
    } catch (e) {
      print('加载用户信息失败: $e');
    }
  }

  /// 保存用户信息到本地存储
  Future<void> _saveUserToStorage() async {
    if (_currentUser != null) {
      try {
        final prefs = await SharedPreferences.getInstance();
        final userJson = _currentUser!.toJson().toString();
        await prefs.setString('current_user', userJson);
      } catch (e) {
        print('保存用户信息失败: $e');
      }
    }
  }

  /// 清除本地存储的用户信息
  Future<void> _clearUserFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('current_user');
    } catch (e) {
      print('清除用户信息失败: $e');
    }
  }

  /// 检查用户权限
  bool hasPermission(String permission) {
    if (_currentUser == null) return false;
    
    // 超级管理员拥有所有权限
    if (_currentUser!.role == AdminRole.admin) {
      return true;
    }
    
    // 根据角色检查权限
    switch (permission) {
      case 'user_management':
        return _currentUser!.role == AdminRole.manager;
      case 'product_management':
        return [AdminRole.manager, AdminRole.staff].contains(_currentUser!.role);
      case 'order_management':
        return [AdminRole.manager, AdminRole.staff].contains(_currentUser!.role);
      case 'chat_service':
        return [AdminRole.manager, AdminRole.staff, AdminRole.customerService]
            .contains(_currentUser!.role);
      case 'system_settings':
        return _currentUser!.role == AdminRole.admin;
      default:
        return false;
    }
  }

  /// 检查是否为客服角色
  bool get isCustomerService => 
      _currentUser?.role == AdminRole.customerService;

  /// 检查是否为管理员角色
  bool get isAdmin => _currentUser?.role == AdminRole.admin;

  /// 检查是否为经理角色
  bool get isManager => _currentUser?.role == AdminRole.manager;
}
