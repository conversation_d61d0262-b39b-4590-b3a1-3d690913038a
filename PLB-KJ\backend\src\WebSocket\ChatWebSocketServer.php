<?php

namespace App\WebSocket;

use Ratchet\MessageComponentInterface;
use Ratchet\ConnectionInterface;
use App\Utils\Logger;
use App\Models\ChatSession;
use App\Models\ChatMessage;

/**
 * 聊天WebSocket服务器
 */
class ChatWebSocketServer implements MessageComponentInterface
{
    protected $clients;
    protected $sessions;
    protected $adminConnections;
    protected $customerConnections;

    public function __construct()
    {
        $this->clients = new \SplObjectStorage;
        $this->sessions = [];
        $this->adminConnections = [];
        $this->customerConnections = [];
        
        Logger::info("ChatWebSocketServer initialized");
    }

    public function onOpen(ConnectionInterface $conn)
    {
        $this->clients->attach($conn);
        Logger::info("New connection: {$conn->resourceId}");
        
        // 发送连接确认
        $conn->send(json_encode([
            'type' => 'connection_established',
            'connection_id' => $conn->resourceId,
            'timestamp' => time()
        ]));
    }

    public function onMessage(ConnectionInterface $from, $msg)
    {
        try {
            $data = json_decode($msg, true);
            
            if (!$data || !isset($data['type'])) {
                $this->sendError($from, 'Invalid message format');
                return;
            }

            Logger::info("Received message", [
                'connection_id' => $from->resourceId,
                'type' => $data['type'],
                'data' => $data
            ]);

            switch ($data['type']) {
                case 'auth':
                    $this->handleAuth($from, $data);
                    break;
                case 'join_session':
                    $this->handleJoinSession($from, $data);
                    break;
                case 'send_message':
                    $this->handleSendMessage($from, $data);
                    break;
                case 'typing':
                    $this->handleTyping($from, $data);
                    break;
                case 'get_sessions':
                    $this->handleGetSessions($from, $data);
                    break;
                default:
                    $this->sendError($from, 'Unknown message type');
            }
        } catch (\Exception $e) {
            Logger::error("Error processing message", [
                'connection_id' => $from->resourceId,
                'error' => $e->getMessage(),
                'message' => $msg
            ]);
            $this->sendError($from, 'Internal server error');
        }
    }

    public function onClose(ConnectionInterface $conn)
    {
        $this->clients->detach($conn);
        
        // 清理连接记录
        $this->cleanupConnection($conn);
        
        Logger::info("Connection closed: {$conn->resourceId}");
    }

    public function onError(ConnectionInterface $conn, \Exception $e)
    {
        Logger::error("WebSocket error", [
            'connection_id' => $conn->resourceId,
            'error' => $e->getMessage()
        ]);
        
        $conn->close();
    }

    /**
     * 处理用户认证
     */
    private function handleAuth(ConnectionInterface $conn, array $data)
    {
        if (!isset($data['user_type']) || !isset($data['user_id'])) {
            $this->sendError($conn, 'Missing authentication data');
            return;
        }

        $userType = $data['user_type']; // 'admin' or 'customer'
        $userId = $data['user_id'];
        $token = $data['token'] ?? null;

        // TODO: 验证token的有效性
        
        // 存储连接信息
        $conn->user_type = $userType;
        $conn->user_id = $userId;
        $conn->authenticated = true;

        if ($userType === 'admin') {
            $this->adminConnections[$userId] = $conn;
        } else {
            $this->customerConnections[$userId] = $conn;
        }

        $this->sendSuccess($conn, 'authenticated', [
            'user_type' => $userType,
            'user_id' => $userId
        ]);

        Logger::info("User authenticated", [
            'connection_id' => $conn->resourceId,
            'user_type' => $userType,
            'user_id' => $userId
        ]);
    }

    /**
     * 处理加入会话
     */
    private function handleJoinSession(ConnectionInterface $conn, array $data)
    {
        if (!$this->isAuthenticated($conn)) {
            $this->sendError($conn, 'Not authenticated');
            return;
        }

        $sessionId = $data['session_id'] ?? null;
        if (!$sessionId) {
            $this->sendError($conn, 'Missing session_id');
            return;
        }

        // 验证用户是否有权限访问此会话
        if (!$this->canAccessSession($conn, $sessionId)) {
            $this->sendError($conn, 'Access denied');
            return;
        }

        $conn->session_id = $sessionId;
        
        if (!isset($this->sessions[$sessionId])) {
            $this->sessions[$sessionId] = [];
        }
        
        $this->sessions[$sessionId][$conn->resourceId] = $conn;

        $this->sendSuccess($conn, 'joined_session', [
            'session_id' => $sessionId
        ]);

        // 通知会话中的其他用户
        $this->broadcastToSession($sessionId, [
            'type' => 'user_joined',
            'user_type' => $conn->user_type,
            'user_id' => $conn->user_id,
            'timestamp' => time()
        ], $conn->resourceId);

        Logger::info("User joined session", [
            'connection_id' => $conn->resourceId,
            'session_id' => $sessionId,
            'user_type' => $conn->user_type
        ]);
    }

    /**
     * 处理发送消息
     */
    private function handleSendMessage(ConnectionInterface $conn, array $data)
    {
        if (!$this->isAuthenticated($conn)) {
            $this->sendError($conn, 'Not authenticated');
            return;
        }

        $sessionId = $data['session_id'] ?? null;
        $content = $data['content'] ?? null;
        $messageType = $data['message_type'] ?? 'text';

        if (!$sessionId || !$content) {
            $this->sendError($conn, 'Missing required fields');
            return;
        }

        try {
            // 保存消息到数据库
            $message = ChatMessage::create([
                'session_id' => $sessionId,
                'sender_type' => $conn->user_type,
                'sender_id' => $conn->user_id,
                'content' => $content,
                'message_type' => $messageType,
                'created_at' => date('Y-m-d H:i:s')
            ]);

            // 构建消息数据
            $messageData = [
                'type' => 'new_message',
                'message' => [
                    'id' => $message['id'],
                    'session_id' => $sessionId,
                    'sender_type' => $conn->user_type,
                    'sender_id' => $conn->user_id,
                    'content' => $content,
                    'message_type' => $messageType,
                    'created_at' => $message['created_at'],
                    'timestamp' => time()
                ]
            ];

            // 广播消息到会话中的所有用户
            $this->broadcastToSession($sessionId, $messageData);

            Logger::info("Message sent", [
                'message_id' => $message['id'],
                'session_id' => $sessionId,
                'sender_type' => $conn->user_type,
                'sender_id' => $conn->user_id
            ]);

        } catch (\Exception $e) {
            Logger::error("Failed to send message", [
                'error' => $e->getMessage(),
                'session_id' => $sessionId
            ]);
            $this->sendError($conn, 'Failed to send message');
        }
    }

    /**
     * 处理打字状态
     */
    private function handleTyping(ConnectionInterface $conn, array $data)
    {
        if (!$this->isAuthenticated($conn)) {
            return;
        }

        $sessionId = $data['session_id'] ?? null;
        $isTyping = $data['is_typing'] ?? false;

        if (!$sessionId) {
            return;
        }

        // 广播打字状态到会话中的其他用户
        $this->broadcastToSession($sessionId, [
            'type' => 'typing_status',
            'user_type' => $conn->user_type,
            'user_id' => $conn->user_id,
            'is_typing' => $isTyping,
            'timestamp' => time()
        ], $conn->resourceId);
    }

    /**
     * 处理获取会话列表（管理员）
     */
    private function handleGetSessions(ConnectionInterface $conn, array $data)
    {
        if (!$this->isAuthenticated($conn) || $conn->user_type !== 'admin') {
            $this->sendError($conn, 'Access denied');
            return;
        }

        try {
            $sessions = ChatSession::getActiveSessions();
            
            $this->sendSuccess($conn, 'sessions_list', [
                'sessions' => $sessions
            ]);

        } catch (\Exception $e) {
            Logger::error("Failed to get sessions", [
                'error' => $e->getMessage()
            ]);
            $this->sendError($conn, 'Failed to get sessions');
        }
    }

    /**
     * 检查用户是否已认证
     */
    private function isAuthenticated(ConnectionInterface $conn): bool
    {
        return isset($conn->authenticated) && $conn->authenticated;
    }

    /**
     * 检查用户是否可以访问会话
     */
    private function canAccessSession(ConnectionInterface $conn, int $sessionId): bool
    {
        // 管理员可以访问所有会话
        if ($conn->user_type === 'admin') {
            return true;
        }

        // 客户只能访问自己的会话
        if ($conn->user_type === 'customer') {
            try {
                $session = ChatSession::findById($sessionId);
                return $session && $session['customer_id'] == $conn->user_id;
            } catch (\Exception $e) {
                return false;
            }
        }

        return false;
    }

    /**
     * 向会话中的所有用户广播消息
     */
    private function broadcastToSession(int $sessionId, array $data, int $excludeConnectionId = null)
    {
        if (!isset($this->sessions[$sessionId])) {
            return;
        }

        $message = json_encode($data);
        
        foreach ($this->sessions[$sessionId] as $connectionId => $conn) {
            if ($excludeConnectionId && $connectionId === $excludeConnectionId) {
                continue;
            }
            
            try {
                $conn->send($message);
            } catch (\Exception $e) {
                Logger::error("Failed to send message to connection", [
                    'connection_id' => $connectionId,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    /**
     * 发送成功响应
     */
    private function sendSuccess(ConnectionInterface $conn, string $type, array $data = [])
    {
        $conn->send(json_encode([
            'type' => $type,
            'success' => true,
            'data' => $data,
            'timestamp' => time()
        ]));
    }

    /**
     * 发送错误响应
     */
    private function sendError(ConnectionInterface $conn, string $message)
    {
        $conn->send(json_encode([
            'type' => 'error',
            'success' => false,
            'error' => $message,
            'timestamp' => time()
        ]));
    }

    /**
     * 清理连接
     */
    private function cleanupConnection(ConnectionInterface $conn)
    {
        // 从管理员连接中移除
        if (isset($conn->user_type) && $conn->user_type === 'admin' && isset($conn->user_id)) {
            unset($this->adminConnections[$conn->user_id]);
        }

        // 从客户连接中移除
        if (isset($conn->user_type) && $conn->user_type === 'customer' && isset($conn->user_id)) {
            unset($this->customerConnections[$conn->user_id]);
        }

        // 从会话中移除
        if (isset($conn->session_id)) {
            $sessionId = $conn->session_id;
            if (isset($this->sessions[$sessionId][$conn->resourceId])) {
                unset($this->sessions[$sessionId][$conn->resourceId]);
                
                // 如果会话中没有用户了，清理会话
                if (empty($this->sessions[$sessionId])) {
                    unset($this->sessions[$sessionId]);
                } else {
                    // 通知会话中的其他用户
                    $this->broadcastToSession($sessionId, [
                        'type' => 'user_left',
                        'user_type' => $conn->user_type ?? 'unknown',
                        'user_id' => $conn->user_id ?? 0,
                        'timestamp' => time()
                    ]);
                }
            }
        }
    }
}
