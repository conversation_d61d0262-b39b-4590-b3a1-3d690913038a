import 'dart:convert';
import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../models/chat_models.dart';

/// 管理端聊天服务（客服端）
class AdminChatService extends ChangeNotifier {
  static const String _baseUrl = 'http://localhost/PLB-KJ/backend/public/api';
  
  bool _isConnected = false;
  String? _authToken;
  
  List<ChatSession> _sessions = [];
  Map<String, List<ChatMessage>> _messages = {};
  Timer? _refreshTimer;
  
  // Getters
  bool get isConnected => _isConnected;
  List<ChatSession> get sessions => _sessions;
  Map<String, List<ChatMessage>> get messages => _messages;
  
  // 按状态过滤的会话
  List<ChatSession> get waitingSessions => 
      _sessions.where((s) => s.status == 'waiting').toList();
  List<ChatSession> get activeSessions => 
      _sessions.where((s) => s.status == 'active').toList();
  List<ChatSession> get closedSessions => 
      _sessions.where((s) => s.status == 'closed').toList();
  
  /// 初始化管理端聊天服务
  Future<void> initialize() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('auth_token');
    
    if (token != null) {
      _authToken = token;
      _isConnected = true;
      
      // 加载会话列表
      await loadSessions();
      
      // 启动定时刷新（每30秒）
      _startRefreshTimer();
      
      notifyListeners();
    }
  }
  
  /// 启动定时刷新
  void _startRefreshTimer() {
    _refreshTimer?.cancel();
    _refreshTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      loadSessions();
    });
  }
  
  /// 获取请求头
  Map<String, String> _getHeaders() {
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $_authToken',
    };
  }
  
  /// 加载会话列表
  Future<void> loadSessions() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/chat/sessions'),
        headers: _getHeaders(),
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          _sessions = (data['data']['sessions'] as List)
              .map((json) => ChatSession.fromJson(json))
              .toList();
          notifyListeners();
        }
      }
    } catch (e) {
      print('加载会话列表失败: $e');
    }
  }
  
  /// 获取会话消息
  Future<List<ChatMessage>> getSessionMessages(int sessionId) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/chat/sessions/$sessionId/messages'),
        headers: _getHeaders(),
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          final messages = (data['data']['messages'] as List)
              .map((json) => ChatMessage.fromJson(json))
              .toList();
          
          _messages[sessionId.toString()] = messages;
          notifyListeners();
          
          return messages;
        }
      }
    } catch (e) {
      print('获取消息失败: $e');
    }
    
    return [];
  }
  
  /// 发送消息（客服回复）
  Future<ChatMessage?> sendMessage({
    required int sessionId,
    required String content,
    String messageType = 'text',
    int? replyToMessageId,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/chat/sessions/$sessionId/messages'),
        headers: _getHeaders(),
        body: json.encode({
          'content': content,
          'message_type': messageType,
          'reply_to_message_id': replyToMessageId,
        }),
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          final message = ChatMessage.fromJson(data['data']['message']);
          
          // 添加到本地消息列表
          final sessionKey = sessionId.toString();
          if (!_messages.containsKey(sessionKey)) {
            _messages[sessionKey] = [];
          }
          _messages[sessionKey]!.add(message);
          
          // 更新会话的最后消息时间
          _updateSessionLastMessage(sessionId);
          
          notifyListeners();
          return message;
        }
      }
    } catch (e) {
      print('发送消息失败: $e');
    }
    return null;
  }
  
  /// 接受会话（客服接入）
  Future<bool> acceptSession(int sessionId) async {
    try {
      final response = await http.put(
        Uri.parse('$_baseUrl/chat/sessions/$sessionId/accept'),
        headers: _getHeaders(),
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          // 更新本地会话状态
          final sessionIndex = _sessions.indexWhere((s) => s.id == sessionId);
          if (sessionIndex != -1) {
            _sessions[sessionIndex] = _sessions[sessionIndex].copyWith(
              status: 'active',
            );
            notifyListeners();
          }
          return true;
        }
      }
    } catch (e) {
      print('接受会话失败: $e');
    }
    return false;
  }
  
  /// 关闭会话
  Future<bool> closeSession({
    required int sessionId,
    int? rating,
    String? feedback,
  }) async {
    try {
      final response = await http.put(
        Uri.parse('$_baseUrl/chat/sessions/$sessionId/close'),
        headers: _getHeaders(),
        body: json.encode({
          'rating': rating,
          'feedback': feedback,
        }),
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          // 更新本地会话状态
          final sessionIndex = _sessions.indexWhere((s) => s.id == sessionId);
          if (sessionIndex != -1) {
            _sessions[sessionIndex] = _sessions[sessionIndex].copyWith(
              status: 'closed',
              endedAt: DateTime.now(),
            );
            notifyListeners();
          }
          return true;
        }
      }
    } catch (e) {
      print('关闭会话失败: $e');
    }
    return false;
  }
  
  /// 获取聊天统计
  Future<Map<String, dynamic>?> getChatStats() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/chat/stats'),
        headers: _getHeaders(),
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return data['data']['stats'];
        }
      }
    } catch (e) {
      print('获取统计失败: $e');
    }
    return null;
  }
  
  /// 刷新数据
  Future<void> refresh() async {
    await loadSessions();
    
    // 刷新当前查看的消息
    for (final sessionId in _messages.keys) {
      await getSessionMessages(int.parse(sessionId));
    }
  }
  
  /// 更新会话最后消息时间
  void _updateSessionLastMessage(int sessionId) {
    final sessionIndex = _sessions.indexWhere((s) => s.id == sessionId);
    if (sessionIndex != -1) {
      _sessions[sessionIndex] = _sessions[sessionIndex].copyWith(
        lastMessageAt: DateTime.now(),
      );
    }
  }
  
  /// 断开连接
  void disconnect() {
    _refreshTimer?.cancel();
    _isConnected = false;
    _authToken = null;
    _sessions.clear();
    _messages.clear();
    notifyListeners();
  }
  
  @override
  void dispose() {
    disconnect();
    super.dispose();
  }
}
