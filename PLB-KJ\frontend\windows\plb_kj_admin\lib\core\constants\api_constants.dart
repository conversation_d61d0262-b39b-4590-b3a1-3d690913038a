/// API常量配置
class ApiConstants {
  // 基础URL
  static const String baseUrl = 'http://localhost:8080/api';
  
  // 认证相关
  static const String login = '/admin/login';
  static const String validateToken = '/auth/validate';
  static const String captcha = '/auth/captcha';
  
  // 用户管理
  static const String users = '/admin/users';
  static const String userById = '/admin/users/{id}';
  static const String userStatus = '/admin/users/{id}/status';
  static const String userPassword = '/admin/users/{id}/password';
  static const String userBatch = '/admin/users/batch';
  
  // 客户管理
  static const String customers = '/admin/customers';
  static const String customerById = '/admin/customers/{id}';
  
  // 聊天系统
  static const String chatSessions = '/chat/sessions';
  static const String chatSessionById = '/chat/sessions/{id}';
  static const String chatMessages = '/chat/sessions/{id}/messages';
  static const String chatAccept = '/chat/sessions/{id}/accept';
  static const String chatClose = '/chat/sessions/{id}/close';
  static const String chatRead = '/chat/sessions/{id}/read';
  static const String chatStats = '/chat/stats';
  
  // 商品管理
  static const String products = '/admin/products';
  static const String productById = '/admin/products/{id}';
  static const String categories = '/admin/categories';
  static const String categoryById = '/admin/categories/{id}';
  
  // 订单管理
  static const String orders = '/admin/orders';
  static const String orderById = '/admin/orders/{id}';
  static const String orderStatus = '/admin/orders/{id}/status';
  
  // 统计数据
  static const String stats = '/admin/stats';
  static const String userStats = '/admin/stats/users';
  static const String orderStats = '/admin/stats/orders';
  
  // 系统设置
  static const String settings = '/admin/settings';
  static const String settingById = '/admin/settings/{key}';
  
  // 文件上传
  static const String fileUpload = '/admin/files/upload';
  
  // 健康检查
  static const String health = '/health';
  
  /// 替换URL中的参数
  static String replaceUrlParams(String url, Map<String, dynamic> params) {
    String result = url;
    params.forEach((key, value) {
      result = result.replaceAll('{$key}', value.toString());
    });
    return result;
  }
}

/// HTTP状态码
class HttpStatus {
  static const int ok = 200;
  static const int created = 201;
  static const int noContent = 204;
  static const int badRequest = 400;
  static const int unauthorized = 401;
  static const int forbidden = 403;
  static const int notFound = 404;
  static const int conflict = 409;
  static const int internalServerError = 500;
}

/// API响应状态
class ApiResponseStatus {
  static const String success = 'success';
  static const String error = 'error';
  static const String warning = 'warning';
}
