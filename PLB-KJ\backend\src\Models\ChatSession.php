<?php

namespace App\Models;

use App\Helpers\Database;
use PDO;

class ChatSession
{
    private $db;

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * 创建新的聊天会话
     */
    public function create($data)
    {
        $sql = "INSERT INTO plb_kj_chat_sessions 
                (session_id, customer_id, title, category, priority, status, started_at, last_message_at) 
                VALUES (?, ?, ?, ?, ?, 'waiting', NOW(), NOW())";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            $data['session_id'],
            $data['customer_id'],
            $data['title'],
            $data['category'],
            $data['priority']
        ]);

        return $this->db->lastInsertId();
    }

    /**
     * 根据ID获取会话
     */
    public function findById($id)
    {
        $sql = "SELECT s.*, 
                       c.first_name as customer_first_name, 
                       c.last_name as customer_last_name,
                       c.email as customer_email,
                       c.avatar as customer_avatar,
                       c.is_online as customer_is_online,
                       a.first_name as admin_first_name,
                       a.last_name as admin_last_name,
                       a.email as admin_email,
                       a.avatar as admin_avatar,
                       a.is_online as admin_is_online
                FROM plb_kj_chat_sessions s
                LEFT JOIN plb_kj_customers c ON s.customer_id = c.id
                LEFT JOIN plb_kj_admin_users a ON s.admin_user_id = a.id
                WHERE s.id = ?";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$id]);
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * 获取用户的会话列表
     */
    public function findByUser($userId, $userType)
    {
        if ($userType === 'admin') {
            $sql = "SELECT s.*, 
                           c.first_name as customer_first_name, 
                           c.last_name as customer_last_name,
                           c.email as customer_email,
                           c.avatar as customer_avatar,
                           c.is_online as customer_is_online,
                           a.first_name as admin_first_name,
                           a.last_name as admin_last_name,
                           a.email as admin_email,
                           a.avatar as admin_avatar,
                           a.is_online as admin_is_online,
                           (SELECT COUNT(*) FROM plb_kj_chat_messages m 
                            WHERE m.session_id = s.id AND m.is_read = 0 
                            AND m.sender_type = 'customer') as unread_count
                    FROM plb_kj_chat_sessions s
                    LEFT JOIN plb_kj_customers c ON s.customer_id = c.id
                    LEFT JOIN plb_kj_admin_users a ON s.admin_user_id = a.id
                    WHERE s.admin_user_id IS NULL OR s.admin_user_id = ?
                    ORDER BY s.last_message_at DESC";
        } else {
            $sql = "SELECT s.*, 
                           c.first_name as customer_first_name, 
                           c.last_name as customer_last_name,
                           c.email as customer_email,
                           c.avatar as customer_avatar,
                           c.is_online as customer_is_online,
                           a.first_name as admin_first_name,
                           a.last_name as admin_last_name,
                           a.email as admin_email,
                           a.avatar as admin_avatar,
                           a.is_online as admin_is_online,
                           (SELECT COUNT(*) FROM plb_kj_chat_messages m 
                            WHERE m.session_id = s.id AND m.is_read = 0 
                            AND m.sender_type = 'admin') as unread_count
                    FROM plb_kj_chat_sessions s
                    LEFT JOIN plb_kj_customers c ON s.customer_id = c.id
                    LEFT JOIN plb_kj_admin_users a ON s.admin_user_id = a.id
                    WHERE s.customer_id = ?
                    ORDER BY s.last_message_at DESC";
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId]);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * 更新会话状态
     */
    public function updateStatus($id, $status, $adminUserId = null)
    {
        if ($adminUserId) {
            $sql = "UPDATE plb_kj_chat_sessions 
                    SET status = ?, admin_user_id = ?, updated_at = NOW() 
                    WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$status, $adminUserId, $id]);
        } else {
            $sql = "UPDATE plb_kj_chat_sessions 
                    SET status = ?, updated_at = NOW() 
                    WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$status, $id]);
        }

        return $stmt->rowCount();
    }

    /**
     * 关闭会话
     */
    public function close($id, $rating = null, $feedback = null)
    {
        $sql = "UPDATE plb_kj_chat_sessions 
                SET status = 'closed', ended_at = NOW(), 
                    customer_satisfaction_rating = ?, customer_feedback = ?, updated_at = NOW() 
                WHERE id = ?";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$rating, $feedback, $id]);
        
        return $stmt->rowCount();
    }

    /**
     * 更新最后消息时间
     */
    public function updateLastMessageTime($id)
    {
        $sql = "UPDATE plb_kj_chat_sessions SET last_message_at = NOW() WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$id]);
        
        return $stmt->rowCount();
    }

    /**
     * 检查用户是否有会话访问权限
     */
    public function hasAccess($sessionId, $userId, $userType)
    {
        if ($userType === 'admin') {
            $sql = "SELECT id FROM plb_kj_chat_sessions 
                    WHERE id = ? AND (admin_user_id IS NULL OR admin_user_id = ?)";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$sessionId, $userId]);
        } else {
            $sql = "SELECT id FROM plb_kj_chat_sessions WHERE id = ? AND customer_id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$sessionId, $userId]);
        }
        
        return $stmt->fetch() !== false;
    }

    /**
     * 获取等待中的会话
     */
    public function getWaitingSessions()
    {
        $sql = "SELECT s.*, 
                       c.first_name as customer_first_name, 
                       c.last_name as customer_last_name,
                       c.email as customer_email,
                       c.avatar as customer_avatar,
                       c.is_online as customer_is_online
                FROM plb_kj_chat_sessions s
                LEFT JOIN plb_kj_customers c ON s.customer_id = c.id
                WHERE s.status = 'waiting'
                ORDER BY s.started_at ASC";
        
        $stmt = $this->db->query($sql);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * 自动分配客服
     */
    public function autoAssignAgent($sessionId)
    {
        // 查找在线且会话数最少的客服
        $sql = "SELECT au.id, COUNT(cs.id) as active_sessions
                FROM plb_kj_admin_users au
                LEFT JOIN plb_kj_chat_sessions cs ON au.id = cs.admin_user_id AND cs.status = 'active'
                WHERE au.role = 'customer_service' 
                  AND au.status = 1 
                  AND au.is_online = 1
                GROUP BY au.id
                HAVING active_sessions < 10
                ORDER BY active_sessions ASC, RAND()
                LIMIT 1";
        
        $stmt = $this->db->query($sql);
        $agent = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($agent) {
            $this->updateStatus($sessionId, 'active', $agent['id']);
            return $agent;
        }

        return null;
    }

    /**
     * 获取聊天统计
     */
    public function getStats($date = null)
    {
        if (!$date) {
            $date = date('Y-m-d');
        }

        $sql = "SELECT 
                    COUNT(*) as total_sessions,
                    SUM(CASE WHEN status = 'waiting' THEN 1 ELSE 0 END) as waiting_sessions,
                    SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_sessions,
                    SUM(CASE WHEN status = 'closed' THEN 1 ELSE 0 END) as closed_sessions,
                    AVG(customer_satisfaction_rating) as avg_rating,
                    AVG(CASE WHEN ended_at IS NOT NULL 
                        THEN TIMESTAMPDIFF(SECOND, started_at, ended_at) 
                        ELSE NULL END) as avg_duration
                FROM plb_kj_chat_sessions 
                WHERE DATE(started_at) = ?";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$date]);
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * 获取客户的活跃会话
     */
    public function getActiveSessionByCustomer($customerId)
    {
        $sql = "SELECT * FROM plb_kj_chat_sessions 
                WHERE customer_id = ? AND status IN ('waiting', 'active') 
                ORDER BY created_at DESC LIMIT 1";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$customerId]);
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
}
