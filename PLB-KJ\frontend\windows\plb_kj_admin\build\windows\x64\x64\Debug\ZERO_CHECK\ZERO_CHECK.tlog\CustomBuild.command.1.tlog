^D:\PLB-LINKS\PLB-KJ\FRONTEND\WINDOWS\PLB_KJ_ADMIN\BUILD\WINDOWS\X64\CMAKEFILES\B52CEDD17AEF2B6B054E647F5EBECACA\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows -BD:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/build/windows/x64 --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/build/windows/x64/plb_kj_admin.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
