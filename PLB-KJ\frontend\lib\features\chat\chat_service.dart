import 'dart:convert';
import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'models/chat_models.dart';

class ChatService extends ChangeNotifier {
  static const String _baseUrl = 'http://localhost/PLB-KJ/backend/public/api';
  
  bool _isConnected = false;
  String? _currentUserId;
  String? _currentUserType;
  String? _authToken;
  
  List<ChatSession> _sessions = [];
  Map<String, List<ChatMessage>> _messages = {};
  Timer? _refreshTimer;
  
  // Getters
  bool get isConnected => _isConnected;
  List<ChatSession> get sessions => _sessions;
  Map<String, List<ChatMessage>> get messages => _messages;
  
  // 初始化聊天服务
  Future<void> initialize() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('auth_token');
    final userType = prefs.getString('user_type');
    final userId = prefs.getString('user_id');
    
    if (token != null && userType != null && userId != null) {
      _authToken = token;
      _currentUserId = userId;
      _currentUserType = userType;
      _isConnected = true;
      
      // 加载会话列表
      await loadSessions();
      
      // 启动定时刷新（每30秒刷新一次）
      _startRefreshTimer();
      
      notifyListeners();
    }
  }
  
  // 启动定时刷新
  void _startRefreshTimer() {
    _refreshTimer?.cancel();
    _refreshTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      loadSessions();
    });
  }
  
  // 停止定时刷新
  void _stopRefreshTimer() {
    _refreshTimer?.cancel();
    _refreshTimer = null;
  }
  
  // 获取请求头
  Map<String, String> _getHeaders() {
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $_authToken',
    };
  }
  
  // 加载会话列表
  Future<void> loadSessions() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/chat/sessions'),
        headers: _getHeaders(),
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          _sessions = (data['data']['sessions'] as List)
              .map((json) => ChatSession.fromJson(json))
              .toList();
          notifyListeners();
        }
      }
    } catch (e) {
      print('加载会话列表失败: $e');
    }
  }
  
  // 创建新会话
  Future<ChatSession?> createSession({
    String title = '客服咨询',
    String category = 'general',
    String priority = 'normal',
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/chat/sessions'),
        headers: _getHeaders(),
        body: json.encode({
          'title': title,
          'category': category,
          'priority': priority,
        }),
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          final session = ChatSession.fromJson(data['data']['session']);
          _sessions.insert(0, session);
          notifyListeners();
          return session;
        }
      }
    } catch (e) {
      print('创建会话失败: $e');
    }
    return null;
  }
  
  // 获取会话消息
  Future<List<ChatMessage>> getSessionMessages(int sessionId) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/chat/sessions/$sessionId/messages'),
        headers: _getHeaders(),
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          final messages = (data['data']['messages'] as List)
              .map((json) => ChatMessage.fromJson(json))
              .toList();
          
          _messages[sessionId.toString()] = messages;
          notifyListeners();
          
          return messages;
        }
      }
    } catch (e) {
      print('获取消息失败: $e');
    }
    
    return [];
  }
  
  // 发送消息
  Future<ChatMessage?> sendMessage({
    required int sessionId,
    required String content,
    String messageType = 'text',
    int? replyToMessageId,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/chat/sessions/$sessionId/messages'),
        headers: _getHeaders(),
        body: json.encode({
          'content': content,
          'message_type': messageType,
          'reply_to_message_id': replyToMessageId,
        }),
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          final message = ChatMessage.fromJson(data['data']['message']);
          
          // 添加到本地消息列表
          final sessionKey = sessionId.toString();
          if (!_messages.containsKey(sessionKey)) {
            _messages[sessionKey] = [];
          }
          _messages[sessionKey]!.add(message);
          
          // 更新会话的最后消息时间
          final sessionIndex = _sessions.indexWhere((s) => s.id == sessionId);
          if (sessionIndex != -1) {
            _sessions[sessionIndex] = _sessions[sessionIndex].copyWith(
              lastMessageAt: DateTime.now(),
            );
          }
          
          notifyListeners();
          return message;
        }
      }
    } catch (e) {
      print('发送消息失败: $e');
    }
    return null;
  }
  
  // 接受会话（管理员）
  Future<bool> acceptSession(int sessionId) async {
    try {
      final response = await http.put(
        Uri.parse('$_baseUrl/chat/sessions/$sessionId/accept'),
        headers: _getHeaders(),
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          // 更新本地会话状态
          final sessionIndex = _sessions.indexWhere((s) => s.id == sessionId);
          if (sessionIndex != -1) {
            _sessions[sessionIndex] = _sessions[sessionIndex].copyWith(
              status: 'active',
              adminUserId: int.parse(_currentUserId!),
            );
            notifyListeners();
          }
          return true;
        }
      }
    } catch (e) {
      print('接受会话失败: $e');
    }
    return false;
  }
  
  // 关闭会话
  Future<bool> closeSession({
    required int sessionId,
    int? rating,
    String? feedback,
  }) async {
    try {
      final response = await http.put(
        Uri.parse('$_baseUrl/chat/sessions/$sessionId/close'),
        headers: _getHeaders(),
        body: json.encode({
          'rating': rating,
          'feedback': feedback,
        }),
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          // 更新本地会话状态
          final sessionIndex = _sessions.indexWhere((s) => s.id == sessionId);
          if (sessionIndex != -1) {
            _sessions[sessionIndex] = _sessions[sessionIndex].copyWith(
              status: 'closed',
              endedAt: DateTime.now(),
              customerSatisfactionRating: rating,
              customerFeedback: feedback,
            );
            notifyListeners();
          }
          return true;
        }
      }
    } catch (e) {
      print('关闭会话失败: $e');
    }
    return false;
  }
  
  // 标记消息已读
  Future<bool> markMessagesRead(int sessionId, List<int> messageIds) async {
    try {
      final response = await http.put(
        Uri.parse('$_baseUrl/chat/sessions/$sessionId/read'),
        headers: _getHeaders(),
        body: json.encode({
          'message_ids': messageIds,
        }),
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          // 更新本地消息状态
          final sessionKey = sessionId.toString();
          if (_messages.containsKey(sessionKey)) {
            for (final message in _messages[sessionKey]!) {
              if (messageIds.contains(message.id)) {
                message.isRead = true;
              }
            }
            notifyListeners();
          }
          return true;
        }
      }
    } catch (e) {
      print('标记消息已读失败: $e');
    }
    return false;
  }
  
  // 获取聊天统计
  Future<Map<String, dynamic>?> getChatStats() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/chat/stats'),
        headers: _getHeaders(),
      );
      
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success'] == true) {
          return data['data']['stats'];
        }
      }
    } catch (e) {
      print('获取统计失败: $e');
    }
    return null;
  }
  
  // 刷新会话和消息
  Future<void> refresh() async {
    await loadSessions();
    
    // 刷新当前查看的消息
    for (final sessionId in _messages.keys) {
      await getSessionMessages(int.parse(sessionId));
    }
  }
  
  // 断开连接
  void disconnect() {
    _stopRefreshTimer();
    _isConnected = false;
    _authToken = null;
    _currentUserId = null;
    _currentUserType = null;
    _sessions.clear();
    _messages.clear();
    notifyListeners();
  }
  
  @override
  void dispose() {
    disconnect();
    super.dispose();
  }
  
  // 模拟实时功能的辅助方法
  void sendTypingStatus(int sessionId, bool isTyping) {
    // HTTP API 不支持实时输入状态，这里只是占位
    // 可以考虑使用轮询或其他方式实现
  }
}
