import 'package:flutter/material.dart';
import '../models/chat_models.dart';

class ChatStatsWidget extends StatelessWidget {
  final List<ChatSession> sessions;

  const ChatStatsWidget({
    Key? key,
    required this.sessions,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final stats = _calculateStats();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              title: '总会话',
              value: stats['total'].toString(),
              icon: Icons.chat_bubble_outline,
              color: Colors.blue,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              title: '等待中',
              value: stats['waiting'].toString(),
              icon: Icons.schedule,
              color: Colors.orange,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              title: '进行中',
              value: stats['active'].toString(),
              icon: Icons.chat,
              color: Colors.green,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              title: '已结束',
              value: stats['closed'].toString(),
              icon: Icons.check_circle,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: color,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                value,
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[700],
            ),
          ),
        ],
      ),
    );
  }

  Map<String, int> _calculateStats() {
    int total = sessions.length;
    int waiting = sessions.where((s) => s.status == 'waiting').length;
    int active = sessions.where((s) => s.status == 'active').length;
    int closed = sessions.where((s) => s.status == 'closed').length;

    return {
      'total': total,
      'waiting': waiting,
      'active': active,
      'closed': closed,
    };
  }
}
