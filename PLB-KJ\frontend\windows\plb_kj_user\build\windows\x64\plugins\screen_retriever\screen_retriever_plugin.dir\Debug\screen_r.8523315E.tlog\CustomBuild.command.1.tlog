^D:\PLB-LINKS\PLB-KJ\FRONTEND\WINDOWS\PLB_KJ_USER\WINDOWS\FLUTTER\EPHEMERAL\.PLUGIN_SYMLINKS\SCREEN_RETRIEVER\WINDOWS\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_user/windows -BD:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_user/build/windows/x64 --check-stamp-file D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_user/build/windows/x64/plugins/screen_retriever/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
