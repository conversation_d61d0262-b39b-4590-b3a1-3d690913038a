<?php

namespace App\Models;

use App\Helpers\Database;
use PDO;

class ChatMessage
{
    private $db;

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * 创建新消息
     */
    public function create($data)
    {
        $sql = "INSERT INTO plb_kj_chat_messages 
                (session_id, sender_type, sender_id, message_type, content, reply_to_message_id, created_at) 
                VALUES (?, ?, ?, ?, ?, ?, NOW())";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            $data['session_id'],
            $data['sender_type'],
            $data['sender_id'],
            $data['message_type'],
            $data['content'],
            $data['reply_to_message_id'] ?? null
        ]);

        return $this->db->lastInsertId();
    }

    /**
     * 根据ID获取消息
     */
    public function findById($id)
    {
        $sql = "SELECT m.*, 
                       CASE 
                           WHEN m.sender_type = 'customer' THEN c.first_name
                           WHEN m.sender_type = 'admin' THEN a.first_name
                       END as sender_first_name,
                       CASE 
                           WHEN m.sender_type = 'customer' THEN c.last_name
                           WHEN m.sender_type = 'admin' THEN a.last_name
                       END as sender_last_name,
                       CASE 
                           WHEN m.sender_type = 'customer' THEN c.avatar
                           WHEN m.sender_type = 'admin' THEN a.avatar
                       END as sender_avatar
                FROM plb_kj_chat_messages m
                LEFT JOIN plb_kj_customers c ON m.sender_type = 'customer' AND m.sender_id = c.id
                LEFT JOIN plb_kj_admin_users a ON m.sender_type = 'admin' AND m.sender_id = a.id
                WHERE m.id = ?";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$id]);
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * 获取会话的消息列表
     */
    public function findBySession($sessionId, $limit = 50, $offset = 0)
    {
        $sql = "SELECT m.*, 
                       CASE 
                           WHEN m.sender_type = 'customer' THEN c.first_name
                           WHEN m.sender_type = 'admin' THEN a.first_name
                       END as sender_first_name,
                       CASE 
                           WHEN m.sender_type = 'customer' THEN c.last_name
                           WHEN m.sender_type = 'admin' THEN a.last_name
                       END as sender_last_name,
                       CASE 
                           WHEN m.sender_type = 'customer' THEN c.avatar
                           WHEN m.sender_type = 'admin' THEN a.avatar
                       END as sender_avatar
                FROM plb_kj_chat_messages m
                LEFT JOIN plb_kj_customers c ON m.sender_type = 'customer' AND m.sender_id = c.id
                LEFT JOIN plb_kj_admin_users a ON m.sender_type = 'admin' AND m.sender_id = a.id
                WHERE m.session_id = ?
                ORDER BY m.created_at ASC
                LIMIT ? OFFSET ?";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$sessionId, $limit, $offset]);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * 标记消息为已读
     */
    public function markAsRead($messageIds, $sessionId = null)
    {
        if (empty($messageIds)) {
            return 0;
        }

        $placeholders = str_repeat('?,', count($messageIds) - 1) . '?';
        
        if ($sessionId) {
            $sql = "UPDATE plb_kj_chat_messages 
                    SET is_read = 1, read_at = NOW() 
                    WHERE id IN ($placeholders) AND session_id = ?";
            $params = array_merge($messageIds, [$sessionId]);
        } else {
            $sql = "UPDATE plb_kj_chat_messages 
                    SET is_read = 1, read_at = NOW() 
                    WHERE id IN ($placeholders)";
            $params = $messageIds;
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->rowCount();
    }

    /**
     * 获取未读消息数量
     */
    public function getUnreadCount($sessionId, $senderType = null)
    {
        if ($senderType) {
            $sql = "SELECT COUNT(*) as count FROM plb_kj_chat_messages 
                    WHERE session_id = ? AND is_read = 0 AND sender_type = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$sessionId, $senderType]);
        } else {
            $sql = "SELECT COUNT(*) as count FROM plb_kj_chat_messages 
                    WHERE session_id = ? AND is_read = 0";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$sessionId]);
        }
        
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return (int)$result['count'];
    }

    /**
     * 获取会话的最后一条消息
     */
    public function getLastMessage($sessionId)
    {
        $sql = "SELECT m.*, 
                       CASE 
                           WHEN m.sender_type = 'customer' THEN c.first_name
                           WHEN m.sender_type = 'admin' THEN a.first_name
                       END as sender_first_name,
                       CASE 
                           WHEN m.sender_type = 'customer' THEN c.last_name
                           WHEN m.sender_type = 'admin' THEN a.last_name
                       END as sender_last_name,
                       CASE 
                           WHEN m.sender_type = 'customer' THEN c.avatar
                           WHEN m.sender_type = 'admin' THEN a.avatar
                       END as sender_avatar
                FROM plb_kj_chat_messages m
                LEFT JOIN plb_kj_customers c ON m.sender_type = 'customer' AND m.sender_id = c.id
                LEFT JOIN plb_kj_admin_users a ON m.sender_type = 'admin' AND m.sender_id = a.id
                WHERE m.session_id = ?
                ORDER BY m.created_at DESC
                LIMIT 1";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$sessionId]);
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * 删除消息
     */
    public function delete($id)
    {
        $sql = "DELETE FROM plb_kj_chat_messages WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$id]);
        
        return $stmt->rowCount();
    }

    /**
     * 搜索消息
     */
    public function search($sessionId, $keyword, $limit = 20, $offset = 0)
    {
        $sql = "SELECT m.*, 
                       CASE 
                           WHEN m.sender_type = 'customer' THEN c.first_name
                           WHEN m.sender_type = 'admin' THEN a.first_name
                       END as sender_first_name,
                       CASE 
                           WHEN m.sender_type = 'customer' THEN c.last_name
                           WHEN m.sender_type = 'admin' THEN a.last_name
                       END as sender_last_name,
                       CASE 
                           WHEN m.sender_type = 'customer' THEN c.avatar
                           WHEN m.sender_type = 'admin' THEN a.avatar
                       END as sender_avatar
                FROM plb_kj_chat_messages m
                LEFT JOIN plb_kj_customers c ON m.sender_type = 'customer' AND m.sender_id = c.id
                LEFT JOIN plb_kj_admin_users a ON m.sender_type = 'admin' AND m.sender_id = a.id
                WHERE m.session_id = ? AND m.content LIKE ?
                ORDER BY m.created_at DESC
                LIMIT ? OFFSET ?";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$sessionId, "%$keyword%", $limit, $offset]);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * 获取消息统计
     */
    public function getStats($date = null)
    {
        if (!$date) {
            $date = date('Y-m-d');
        }

        $sql = "SELECT 
                    COUNT(*) as total_messages,
                    SUM(CASE WHEN sender_type = 'customer' THEN 1 ELSE 0 END) as customer_messages,
                    SUM(CASE WHEN sender_type = 'admin' THEN 1 ELSE 0 END) as admin_messages,
                    SUM(CASE WHEN message_type = 'text' THEN 1 ELSE 0 END) as text_messages,
                    SUM(CASE WHEN message_type = 'image' THEN 1 ELSE 0 END) as image_messages,
                    SUM(CASE WHEN message_type = 'file' THEN 1 ELSE 0 END) as file_messages,
                    SUM(CASE WHEN message_type = 'audio' THEN 1 ELSE 0 END) as audio_messages
                FROM plb_kj_chat_messages 
                WHERE DATE(created_at) = ?";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$date]);
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * 获取用户发送的消息数量
     */
    public function getUserMessageCount($userId, $userType, $date = null)
    {
        if ($date) {
            $sql = "SELECT COUNT(*) as count FROM plb_kj_chat_messages 
                    WHERE sender_id = ? AND sender_type = ? AND DATE(created_at) = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$userId, $userType, $date]);
        } else {
            $sql = "SELECT COUNT(*) as count FROM plb_kj_chat_messages 
                    WHERE sender_id = ? AND sender_type = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$userId, $userType]);
        }
        
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return (int)$result['count'];
    }

    /**
     * 批量标记会话消息为已读
     */
    public function markSessionMessagesAsRead($sessionId, $senderType)
    {
        $sql = "UPDATE plb_kj_chat_messages 
                SET is_read = 1, read_at = NOW() 
                WHERE session_id = ? AND sender_type = ? AND is_read = 0";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$sessionId, $senderType]);
        
        return $stmt->rowCount();
    }
}
