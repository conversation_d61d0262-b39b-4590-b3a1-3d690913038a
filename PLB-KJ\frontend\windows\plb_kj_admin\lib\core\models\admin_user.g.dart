// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'admin_user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AdminUser _$AdminUserFromJson(Map<String, dynamic> json) => AdminUser(
  id: (json['id'] as num).toInt(),
  username: json['username'] as String,
  email: json['email'] as String,
  firstName: json['first_name'] as String?,
  lastName: json['last_name'] as String?,
  phone: json['phone'] as String?,
  avatar: json['avatar'] as String?,
  role: $enumDecode(_$AdminRoleEnumMap, json['role']),
  department: json['department'] as String?,
  isOnline: json['is_online'] as bool?,
  lastLoginAt: json['last_login_at'] == null
      ? null
      : DateTime.parse(json['last_login_at'] as String),
  status: json['status'] as bool?,
  createdAt: json['created_at'] == null
      ? null
      : DateTime.parse(json['created_at'] as String),
  updatedAt: json['updated_at'] == null
      ? null
      : DateTime.parse(json['updated_at'] as String),
);

Map<String, dynamic> _$AdminUserToJson(AdminUser instance) => <String, dynamic>{
  'id': instance.id,
  'username': instance.username,
  'email': instance.email,
  'first_name': instance.firstName,
  'last_name': instance.lastName,
  'phone': instance.phone,
  'avatar': instance.avatar,
  'role': _$AdminRoleEnumMap[instance.role]!,
  'department': instance.department,
  'is_online': instance.isOnline,
  'last_login_at': instance.lastLoginAt?.toIso8601String(),
  'status': instance.status,
  'created_at': instance.createdAt?.toIso8601String(),
  'updated_at': instance.updatedAt?.toIso8601String(),
};

const _$AdminRoleEnumMap = {
  AdminRole.admin: 'admin',
  AdminRole.manager: 'manager',
  AdminRole.staff: 'staff',
  AdminRole.customerService: 'customer_service',
};

AdminLoginRequest _$AdminLoginRequestFromJson(Map<String, dynamic> json) =>
    AdminLoginRequest(
      username: json['username'] as String,
      password: json['password'] as String,
      captcha: json['captcha'] as String?,
    );

Map<String, dynamic> _$AdminLoginRequestToJson(AdminLoginRequest instance) =>
    <String, dynamic>{
      'username': instance.username,
      'password': instance.password,
      'captcha': instance.captcha,
    };

AdminLoginResponse _$AdminLoginResponseFromJson(Map<String, dynamic> json) =>
    AdminLoginResponse(
      token: json['token'] as String,
      admin: AdminUser.fromJson(json['user'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$AdminLoginResponseToJson(AdminLoginResponse instance) =>
    <String, dynamic>{'token': instance.token, 'user': instance.admin};

AdminUserRequest _$AdminUserRequestFromJson(Map<String, dynamic> json) =>
    AdminUserRequest(
      username: json['username'] as String,
      email: json['email'] as String,
      password: json['password'] as String?,
      firstName: json['first_name'] as String?,
      lastName: json['last_name'] as String?,
      phone: json['phone'] as String?,
      avatar: json['avatar'] as String?,
      role: $enumDecode(_$AdminRoleEnumMap, json['role']),
      department: json['department'] as String?,
      status: json['status'] as bool?,
    );

Map<String, dynamic> _$AdminUserRequestToJson(AdminUserRequest instance) =>
    <String, dynamic>{
      'username': instance.username,
      'email': instance.email,
      'password': instance.password,
      'first_name': instance.firstName,
      'last_name': instance.lastName,
      'phone': instance.phone,
      'avatar': instance.avatar,
      'role': _$AdminRoleEnumMap[instance.role]!,
      'department': instance.department,
      'status': instance.status,
    };
