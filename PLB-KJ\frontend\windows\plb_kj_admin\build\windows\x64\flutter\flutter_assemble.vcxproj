﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Profile|x64">
      <Configuration>Profile</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{5EB28978-D6CB-3660-8D94-9E4A2DC0EB8C}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>flutter_assemble</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\build\windows\x64\CMakeFiles\ebec1568a24125cc71d8f4633964e2e6\flutter_windows.dll.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Generating D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows/flutter/ephemeral/flutter_windows.dll, D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows/flutter/ephemeral/flutter_export.h, D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows/flutter/ephemeral/flutter_windows.h, D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows/flutter/ephemeral/flutter_messenger.h, D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows/flutter/ephemeral/flutter_plugin_registrar.h, D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows/flutter/ephemeral/flutter_texture_registrar.h, D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows/flutter/ephemeral/cpp_client_wrapper/core_implementations.cc, D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows/flutter/ephemeral/cpp_client_wrapper/standard_codec.cc, D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows/flutter/ephemeral/cpp_client_wrapper/plugin_registrar.cc, D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows/flutter/ephemeral/cpp_client_wrapper/flutter_engine.cc, D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows/flutter/ephemeral/cpp_client_wrapper/flutter_view_controller.cc, _phony_</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=D:\flutter PROJECT_DIR=D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin FLUTTER_ROOT=D:\flutter FLUTTER_EPHEMERAL_DIR=D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral PROJECT_DIR=D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin FLUTTER_TARGET=lib\main.dart DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzUuMC0wLjEucHJl,RkxVVFRFUl9DSEFOTkVMPWJldGE=,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049YzBmMmExZGQ2MA==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049MWM5YzIwZTdjMw==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My45LjAgKGJ1aWxkIDMuOS4wLTMzMy4yLmJldGEp DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=true PACKAGE_CONFIG=D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\.dart_tool\package_config.json D:/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\flutter_windows.dll;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\flutter_export.h;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\flutter_windows.h;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\flutter_messenger.h;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\flutter_plugin_registrar.h;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\flutter_texture_registrar.h;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\cpp_client_wrapper\core_implementations.cc;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\cpp_client_wrapper\standard_codec.cc;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\cpp_client_wrapper\plugin_registrar.cc;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\cpp_client_wrapper\flutter_engine.cc;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\cpp_client_wrapper\flutter_view_controller.cc;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\build\windows\x64\flutter\_phony_</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</VerifyInputsAndOutputsExist>
      <Message Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">Generating D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows/flutter/ephemeral/flutter_windows.dll, D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows/flutter/ephemeral/flutter_export.h, D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows/flutter/ephemeral/flutter_windows.h, D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows/flutter/ephemeral/flutter_messenger.h, D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows/flutter/ephemeral/flutter_plugin_registrar.h, D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows/flutter/ephemeral/flutter_texture_registrar.h, D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows/flutter/ephemeral/cpp_client_wrapper/core_implementations.cc, D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows/flutter/ephemeral/cpp_client_wrapper/standard_codec.cc, D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows/flutter/ephemeral/cpp_client_wrapper/plugin_registrar.cc, D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows/flutter/ephemeral/cpp_client_wrapper/flutter_engine.cc, D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows/flutter/ephemeral/cpp_client_wrapper/flutter_view_controller.cc, _phony_</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=D:\flutter PROJECT_DIR=D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin FLUTTER_ROOT=D:\flutter FLUTTER_EPHEMERAL_DIR=D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral PROJECT_DIR=D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin FLUTTER_TARGET=lib\main.dart DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzUuMC0wLjEucHJl,RkxVVFRFUl9DSEFOTkVMPWJldGE=,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049YzBmMmExZGQ2MA==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049MWM5YzIwZTdjMw==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My45LjAgKGJ1aWxkIDMuOS4wLTMzMy4yLmJldGEp DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=true PACKAGE_CONFIG=D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\.dart_tool\package_config.json D:/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Profile
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\flutter_windows.dll;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\flutter_export.h;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\flutter_windows.h;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\flutter_messenger.h;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\flutter_plugin_registrar.h;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\flutter_texture_registrar.h;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\cpp_client_wrapper\core_implementations.cc;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\cpp_client_wrapper\standard_codec.cc;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\cpp_client_wrapper\plugin_registrar.cc;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\cpp_client_wrapper\flutter_engine.cc;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\cpp_client_wrapper\flutter_view_controller.cc;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\build\windows\x64\flutter\_phony_</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</VerifyInputsAndOutputsExist>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Generating D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows/flutter/ephemeral/flutter_windows.dll, D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows/flutter/ephemeral/flutter_export.h, D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows/flutter/ephemeral/flutter_windows.h, D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows/flutter/ephemeral/flutter_messenger.h, D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows/flutter/ephemeral/flutter_plugin_registrar.h, D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows/flutter/ephemeral/flutter_texture_registrar.h, D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows/flutter/ephemeral/cpp_client_wrapper/core_implementations.cc, D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows/flutter/ephemeral/cpp_client_wrapper/standard_codec.cc, D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows/flutter/ephemeral/cpp_client_wrapper/plugin_registrar.cc, D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows/flutter/ephemeral/cpp_client_wrapper/flutter_engine.cc, D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows/flutter/ephemeral/cpp_client_wrapper/flutter_view_controller.cc, _phony_</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=D:\flutter PROJECT_DIR=D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin FLUTTER_ROOT=D:\flutter FLUTTER_EPHEMERAL_DIR=D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral PROJECT_DIR=D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin FLUTTER_TARGET=lib\main.dart DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzUuMC0wLjEucHJl,RkxVVFRFUl9DSEFOTkVMPWJldGE=,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049YzBmMmExZGQ2MA==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049MWM5YzIwZTdjMw==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My45LjAgKGJ1aWxkIDMuOS4wLTMzMy4yLmJldGEp DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=true PACKAGE_CONFIG=D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\.dart_tool\package_config.json D:/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\flutter_windows.dll;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\flutter_export.h;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\flutter_windows.h;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\flutter_messenger.h;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\flutter_plugin_registrar.h;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\flutter_texture_registrar.h;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\cpp_client_wrapper\core_implementations.cc;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\cpp_client_wrapper\standard_codec.cc;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\cpp_client_wrapper\plugin_registrar.cc;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\cpp_client_wrapper\flutter_engine.cc;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\cpp_client_wrapper\flutter_view_controller.cc;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\build\windows\x64\flutter\_phony_</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</VerifyInputsAndOutputsExist>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\build\windows\x64\CMakeFiles\8699cef8aaf37f0aaa5326edd5be24d2\flutter_assemble.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\flutter_windows.dll;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\flutter_export.h;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\flutter_windows.h;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\flutter_messenger.h;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\flutter_plugin_registrar.h;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\flutter_texture_registrar.h;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\cpp_client_wrapper\core_implementations.cc;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\cpp_client_wrapper\standard_codec.cc;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\cpp_client_wrapper\plugin_registrar.cc;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\cpp_client_wrapper\flutter_engine.cc;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\cpp_client_wrapper\flutter_view_controller.cc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\build\windows\x64\flutter\CMakeFiles\flutter_assemble</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</VerifyInputsAndOutputsExist>
      <Message Condition="'$(Configuration)|$(Platform)'=='Profile|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">setlocal
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\flutter_windows.dll;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\flutter_export.h;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\flutter_windows.h;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\flutter_messenger.h;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\flutter_plugin_registrar.h;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\flutter_texture_registrar.h;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\cpp_client_wrapper\core_implementations.cc;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\cpp_client_wrapper\standard_codec.cc;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\cpp_client_wrapper\plugin_registrar.cc;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\cpp_client_wrapper\flutter_engine.cc;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\cpp_client_wrapper\flutter_view_controller.cc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\build\windows\x64\flutter\CMakeFiles\flutter_assemble</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</VerifyInputsAndOutputsExist>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\flutter_windows.dll;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\flutter_export.h;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\flutter_windows.h;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\flutter_messenger.h;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\flutter_plugin_registrar.h;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\flutter_texture_registrar.h;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\cpp_client_wrapper\core_implementations.cc;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\cpp_client_wrapper\standard_codec.cc;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\cpp_client_wrapper\plugin_registrar.cc;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\cpp_client_wrapper\flutter_engine.cc;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\cpp_client_wrapper\flutter_view_controller.cc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\build\windows\x64\flutter\CMakeFiles\flutter_assemble</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</VerifyInputsAndOutputsExist>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows/flutter/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows -BD:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/build/windows/x64 --check-stamp-file D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\generated_config.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\build\windows\x64\flutter\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">Building Custom Rule D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows/flutter/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows -BD:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/build/windows/x64 --check-stamp-file D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\generated_config.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\build\windows\x64\flutter\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows/flutter/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows -BD:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/build/windows/x64 --check-stamp-file D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\generated_config.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\build\windows\x64\flutter\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <None Include="D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\build\windows\x64\flutter\CMakeFiles\flutter_assemble">
    </None>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\build\windows\x64\ZERO_CHECK.vcxproj">
      <Project>{ADE84548-982A-304D-ACD9-E4316252A20D}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>