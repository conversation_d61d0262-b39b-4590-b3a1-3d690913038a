import 'package:flutter/material.dart';
import '../../../shared/widgets/custom_card.dart';
import '../../../shared/routes/app_routes.dart';
import '../../../shared/routes/admin_routes.dart';
import '../widgets/admin_sidebar.dart';
import '../widgets/admin_stats_card.dart';
import '../widgets/admin_quick_actions.dart';
import '../widgets/admin_users_management.dart';
import '../../chat/screens/chat_main_screen.dart';

class AdminDashboardScreen extends StatefulWidget {
  const AdminDashboardScreen({Key? key}) : super(key: key);

  @override
  State<AdminDashboardScreen> createState() => _AdminDashboardScreenState();
}

class _AdminDashboardScreenState extends State<AdminDashboardScreen> {
  int _selectedIndex = 0;

  final List<String> _menuItems = [
    '仪表盘',
    '用户管理',
    '产品管理',
    '订单管理',
    '库存管理',
    '客服聊天',
    '财务管理',
    '系统设置',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      body: Row(
        children: [
          // 侧边栏
          AdminSidebar(
            selectedIndex: _selectedIndex,
            menuItems: _menuItems,
            onItemSelected: (index) {
              setState(() {
                _selectedIndex = index;
              });
            },
            onLogout: _handleLogout,
          ),
          
          // 主内容区域
          Expanded(
            child: Column(
              children: [
                // 顶部导航栏
                _buildTopBar(),
                
                // 主内容
                Expanded(
                  child: _buildMainContent(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopBar() {
    return Container(
      height: 70,
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Text(
            _menuItems[_selectedIndex],
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const Spacer(),
          
          // 通知图标
          IconButton(
            onPressed: () {
              // TODO: 显示通知
            },
            icon: Stack(
              children: [
                const Icon(Icons.notifications_outlined, size: 24),
                Positioned(
                  right: 0,
                  top: 0,
                  child: Container(
                    width: 8,
                    height: 8,
                    decoration: const BoxDecoration(
                      color: Colors.red,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(width: 16),
          
          // 用户头像和信息
          Row(
            children: [
              CircleAvatar(
                radius: 18,
                backgroundColor: Theme.of(context).primaryColor,
                child: const Icon(
                  Icons.admin_panel_settings,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text(
                    '管理员',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  Text(
                    '<EMAIL>',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMainContent() {
    switch (_selectedIndex) {
      case 0:
        return _buildDashboardContent();
      case 1:
        return _buildUsersContent();
      case 2:
        return _buildProductsContent();
      case 3:
        return _buildOrdersContent();
      case 4:
        return _buildInventoryContent();
      case 5:
        return _buildChatContent();
      case 6:
        return _buildFinanceContent();
      case 7:
        return _buildSettingsContent();
      default:
        return _buildDashboardContent();
    }
  }

  Widget _buildDashboardContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 统计卡片
          Row(
            children: [
              Expanded(
                child: AdminStatsCard(
                  title: '总用户数',
                  value: '1,234',
                  icon: Icons.people,
                  color: Colors.blue,
                  trend: '+12%',
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: AdminStatsCard(
                  title: '总订单数',
                  value: '5,678',
                  icon: Icons.shopping_cart,
                  color: Colors.green,
                  trend: '+8%',
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: AdminStatsCard(
                  title: '总收入',
                  value: '¥128,000',
                  icon: Icons.attach_money,
                  color: Colors.orange,
                  trend: '+15%',
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: AdminStatsCard(
                  title: '产品数量',
                  value: '456',
                  icon: Icons.inventory,
                  color: Colors.purple,
                  trend: '+5%',
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 32),
          
          // 快捷操作
          const Text(
            '快捷操作',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          
          AdminQuickActions(
            onAddUser: () {
              setState(() {
                _selectedIndex = 1; // 跳转到用户管理
              });
            },
            onAddProduct: () {
              // TODO: 添加产品
            },
            onViewOrders: () {
              setState(() {
                _selectedIndex = 3;
              });
            },
            onViewReports: () {
              // TODO: 查看报表
            },
          ),
          
          const SizedBox(height: 32),
          
          // 最近活动
          const Text(
            '最近活动',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          
          CustomCard(
            child: Column(
              children: [
                _buildActivityItem(
                  icon: Icons.person_add,
                  title: '新用户注册',
                  subtitle: '张三刚刚注册了账户',
                  time: '2分钟前',
                ),
                const Divider(),
                _buildActivityItem(
                  icon: Icons.shopping_cart,
                  title: '新订单',
                  subtitle: '订单 #12345 已创建',
                  time: '5分钟前',
                ),
                const Divider(),
                _buildActivityItem(
                  icon: Icons.payment,
                  title: '付款完成',
                  subtitle: '订单 #12344 付款成功',
                  time: '10分钟前',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActivityItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required String time,
  }) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: Theme.of(context).primaryColor,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Text(
            time,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUsersContent() {
    return const AdminUsersManagement();
  }

  Widget _buildProductsContent() {
    return const Center(
      child: Text(
        '产品管理功能开发中...',
        style: TextStyle(fontSize: 18),
      ),
    );
  }

  Widget _buildOrdersContent() {
    return const Center(
      child: Text(
        '订单管理功能开发中...',
        style: TextStyle(fontSize: 18),
      ),
    );
  }

  Widget _buildInventoryContent() {
    return const Center(
      child: Text(
        '库存管理功能开发中...',
        style: TextStyle(fontSize: 18),
      ),
    );
  }

  Widget _buildChatContent() {
    return const ChatMainScreen();
  }

  Widget _buildFinanceContent() {
    return const Center(
      child: Text(
        '财务管理功能开发中...',
        style: TextStyle(fontSize: 18),
      ),
    );
  }

  Widget _buildSettingsContent() {
    return const Center(
      child: Text(
        '系统设置功能开发中...',
        style: TextStyle(fontSize: 18),
      ),
    );
  }

  void _handleLogout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认退出'),
        content: const Text('您确定要退出管理后台吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pushNamedAndRemoveUntil(
                context,
                AppRoutes.loginSelection,
                (route) => false,
              );
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}
