import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../chat_service.dart';
import '../models/chat_models.dart';
import '../widgets/message_bubble.dart';
import '../widgets/message_input.dart';
import '../widgets/session_info_panel.dart';

class ChatSessionScreen extends StatefulWidget {
  final ChatSession session;

  const ChatSessionScreen({
    Key? key,
    required this.session,
  }) : super(key: key);

  @override
  State<ChatSessionScreen> createState() => _ChatSessionScreenState();
}

class _ChatSessionScreenState extends State<ChatSessionScreen> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _messageController = TextEditingController();
  bool _isTyping = false;
  bool _showSessionInfo = false;

  @override
  void initState() {
    super.initState();
    _loadMessages();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _messageController.dispose();
    super.dispose();
  }

  void _loadMessages() async {
    await context.read<ChatService>().getSessionMessages(widget.session.id);
    _scrollToBottom();
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.session.title,
              style: const TextStyle(fontSize: 16),
            ),
            Text(
              _getSessionStatusText(),
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.normal),
            ),
          ],
        ),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: () {
              setState(() {
                _showSessionInfo = !_showSessionInfo;
              });
            },
            icon: const Icon(Icons.info_outline),
            tooltip: '会话信息',
          ),
          if (widget.session.status == 'waiting')
            IconButton(
              onPressed: _acceptSession,
              icon: const Icon(Icons.check),
              tooltip: '接受会话',
            ),
          if (widget.session.status == 'active')
            IconButton(
              onPressed: _closeSession,
              icon: const Icon(Icons.close),
              tooltip: '结束会话',
            ),
        ],
      ),
      body: Row(
        children: [
          // 聊天区域
          Expanded(
            flex: _showSessionInfo ? 2 : 1,
            child: Column(
              children: [
                // 会话状态栏
                _buildStatusBar(),
                
                // 消息列表
                Expanded(
                  child: Consumer<ChatService>(
                    builder: (context, chatService, child) {
                      final messages = chatService.messages[widget.session.id.toString()] ?? [];
                      
                      if (messages.isEmpty) {
                        return const Center(
                          child: Text(
                            '暂无消息，开始对话吧！',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey,
                            ),
                          ),
                        );
                      }

                      return ListView.builder(
                        controller: _scrollController,
                        padding: const EdgeInsets.all(16),
                        itemCount: messages.length,
                        itemBuilder: (context, index) {
                          final message = messages[index];
                          final isMe = message.senderType == 'admin';
                          final showAvatar = index == 0 || 
                              messages[index - 1].senderId != message.senderId;

                          return MessageBubble(
                            message: message,
                            isMe: isMe,
                            showAvatar: showAvatar,
                            onReply: (replyMessage) {
                              // 实现回复功能
                            },
                          );
                        },
                      );
                    },
                  ),
                ),

                // 消息刷新按钮
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      TextButton.icon(
                        onPressed: () {
                          context.read<ChatService>().getSessionMessages(widget.session.id);
                        },
                        icon: const Icon(Icons.refresh, size: 16),
                        label: const Text('刷新消息'),
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),

                // 消息输入框
                if (widget.session.status == 'active')
                  MessageInput(
                    controller: _messageController,
                    onSend: _sendMessage,
                    onTyping: _handleTyping,
                    enabled: widget.session.status == 'active',
                  ),
              ],
            ),
          ),

          // 会话信息面板
          if (_showSessionInfo)
            Container(
              width: 300,
              decoration: BoxDecoration(
                border: Border(
                  left: BorderSide(color: Colors.grey[300]!),
                ),
              ),
              child: SessionInfoPanel(session: widget.session),
            ),
        ],
      ),
    );
  }

  Widget _buildStatusBar() {
    Color statusColor;
    String statusText;
    IconData statusIcon;

    switch (widget.session.status) {
      case 'waiting':
        statusColor = Colors.orange;
        statusText = '等待客服接入';
        statusIcon = Icons.schedule;
        break;
      case 'active':
        statusColor = Colors.green;
        statusText = '会话进行中';
        statusIcon = Icons.chat;
        break;
      case 'closed':
        statusColor = Colors.grey;
        statusText = '会话已结束';
        statusIcon = Icons.check_circle;
        break;
      default:
        statusColor = Colors.grey;
        statusText = '未知状态';
        statusIcon = Icons.help;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.1),
        border: Border(
          bottom: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Row(
        children: [
          Icon(statusIcon, color: statusColor, size: 16),
          const SizedBox(width: 8),
          Text(
            statusText,
            style: TextStyle(
              color: statusColor,
              fontWeight: FontWeight.w500,
            ),
          ),
          const Spacer(),
          Text(
            '优先级: ${_getPriorityText(widget.session.priority)}',
            style: const TextStyle(
              fontSize: 12,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  String _getSessionStatusText() {
    final customer = widget.session.customer;
    if (customer != null) {
      final onlineStatus = customer.isOnline ? '在线' : '离线';
      return '${customer.fullName} ($onlineStatus)';
    }
    return '客户 ID: ${widget.session.customerId}';
  }

  String _getPriorityText(String priority) {
    switch (priority) {
      case 'low':
        return '低';
      case 'normal':
        return '普通';
      case 'high':
        return '高';
      case 'urgent':
        return '紧急';
      default:
        return '普通';
    }
  }

  void _sendMessage(String content, {String messageType = 'text'}) async {
    if (content.trim().isEmpty) return;

    try {
      await context.read<ChatService>().sendMessage(
        sessionId: widget.session.id,
        content: content,
        messageType: messageType,
      );

      _messageController.clear();
      _scrollToBottom();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('发送消息失败: $e')),
      );
    }
  }

  void _handleTyping(bool isTyping) {
    if (_isTyping != isTyping) {
      _isTyping = isTyping;
      context.read<ChatService>().sendTypingStatus(widget.session.id, isTyping);
    }
  }

  void _acceptSession() async {
    try {
      await context.read<ChatService>().acceptSession(widget.session.id);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('会话已接受')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('接受会话失败: $e')),
      );
    }
  }

  void _closeSession() async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => _buildCloseSessionDialog(),
    );

    if (result != null) {
      try {
        await context.read<ChatService>().closeSession(
          sessionId: widget.session.id,
          rating: result['rating'],
          feedback: result['feedback'],
        );
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('会话已关闭')),
        );
        
        Navigator.pop(context);
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('关闭会话失败: $e')),
        );
      }
    }
  }

  Widget _buildCloseSessionDialog() {
    int rating = 5;
    String feedback = '';

    return StatefulBuilder(
      builder: (context, setState) {
        return AlertDialog(
          title: const Text('结束会话'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('请为本次服务评分：'),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(5, (index) {
                  return IconButton(
                    onPressed: () {
                      setState(() {
                        rating = index + 1;
                      });
                    },
                    icon: Icon(
                      index < rating ? Icons.star : Icons.star_border,
                      color: Colors.amber,
                    ),
                  );
                }),
              ),
              const SizedBox(height: 16),
              TextField(
                decoration: const InputDecoration(
                  labelText: '结束原因（可选）',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
                onChanged: (value) {
                  feedback = value;
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context, {
                  'rating': rating,
                  'feedback': feedback,
                });
              },
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }
}
