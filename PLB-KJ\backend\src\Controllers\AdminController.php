<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\User;
use App\Exceptions\AuthException;
use App\Helpers\Logger;
use App\Services\CaptchaService;

class AdminController extends BaseController
{
    private $userModel;
    private $captchaService;
    
    public function __construct()
    {
        parent::__construct();
        $this->userModel = new User();
        $this->captchaService = CaptchaService::getInstance();
    }

    /**
     * 获取验证码
     *
     * @return array
     */
    public function getCaptcha()
    {
        try {
            // 生成验证码
            $captcha = $this->captchaService->generateCaptcha(4);
            
            // 使用更安全的方式存储验证码（通过加密哈希）
            $captchaHash = $this->captchaService->hashCaptcha($captcha['text']);
            
            // 将验证码哈希存储在会话中
            session_start();
            $_SESSION['captcha_hash'] = $captchaHash;
            $_SESSION['captcha_time'] = time(); // 存储时间戳，用于过期检查
            
            // 设置响应头
            header('Content-Type: application/json; charset=UTF-8');
            http_response_code(200);
            
            return [
                'success' => true,
                'image' => $captcha['image']
            ];
        } catch (\Exception $e) {
            Logger::error("Captcha generation error: {$e->getMessage()}");
            http_response_code(500);
            return [
                'success' => false,
                'error' => '验证码生成失败'
            ];
        }
    }

    /**
     * 管理员登录
     *
     * @return array
     */
    public function login()
    {
        try {
            // 获取POST数据
            $rawData = file_get_contents('php://input');
            $data = json_decode($rawData, true);
            
            Logger::info("Admin login attempt for username: " . ($data['username'] ?? 'unknown'));
            
            if (!isset($data['username']) || !isset($data['password'])) {
                Logger::warning("Missing username or password in login attempt");
                throw new AuthException('用户名和密码是必需的', 400);
            }
            
            // 验证验证码（如果提供了验证码）
            if (isset($data['captcha'])) {
                session_start();
                
                // 检查验证码是否存在
                if (!isset($_SESSION['captcha_hash']) || !isset($_SESSION['captcha_time'])) {
                    Logger::warning("No captcha found in session for user: {$data['username']}");
                    throw new AuthException('验证码已过期，请刷新页面重试', 400);
                }
                
                // 检查验证码是否过期（5分钟有效期）
                if (time() - $_SESSION['captcha_time'] > 300) {
                    unset($_SESSION['captcha_hash']);
                    unset($_SESSION['captcha_time']);
                    Logger::warning("Captcha expired for user: {$data['username']}");
                    throw new AuthException('验证码已过期，请刷新获取新的验证码', 400);
                }
                
                // 验证验证码
                $expectedHash = $_SESSION['captcha_hash'];
                $inputHash = $this->captchaService->hashCaptcha($data['captcha']);
                
                if ($inputHash !== $expectedHash) {
                    // 清除已使用的验证码
                    unset($_SESSION['captcha_hash']);
                    unset($_SESSION['captcha_time']);
                    Logger::warning("Invalid captcha attempt for user: {$data['username']}");
                    throw new AuthException('验证码错误', 400);
                }
                
                // 清除已使用的验证码
                unset($_SESSION['captcha_hash']);
                unset($_SESSION['captcha_time']);
            }
            
            // 查找用户
            $user = $this->userModel->findByUsername($data['username']);
            
            if (!$user) {
                Logger::warning("User not found: {$data['username']}");
                throw new AuthException('用户名或密码错误', 401);
            }
            
            // 验证密码
            if (!password_verify($data['password'], $user['password_hash'])) {
                Logger::warning("Failed login attempt for admin user: {$data['username']}");
                throw new AuthException('用户名或密码错误', 401);
            }
            
            // 检查是否为管理员
            if ($user['role'] !== 'admin') {
                Logger::warning("Non-admin user attempted to login: {$data['username']}");
                throw new AuthException('权限不足，仅管理员可登录', 403);
            }

            // 生成token
            $token = base64_encode($user['id'] . ':' . time() . ':' . bin2hex(random_bytes(16)));
            
            Logger::info("Admin user logged in successfully: {$user['username']}");
            
            // 设置响应头
            header('Content-Type: application/json; charset=UTF-8');
            http_response_code(200);
            
            return [
                'success' => true,
                'message' => '登录成功',
                'data' => [
                    'token' => $token,
                    'user' => [
                        'id' => $user['id'],
                        'username' => $user['username'],
                        'email' => $user['email'],
                        'role' => $user['role']
                    ]
                ]
            ];
        } catch (AuthException $e) {
            Logger::error("Admin login error: {$e->getMessage()}");
            http_response_code($e->getCode());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        } catch (\Exception $e) {
            Logger::error("Unexpected error during admin login: {$e->getMessage()}");
            http_response_code(500);
            return [
                'success' => false,
                'error' => '服务器内部错误'
            ];
        }
    }

    /**
     * 验证token
     */
    public function validateToken()
    {
        try {
            // 获取Authorization头
            $headers = getallheaders();
            $authHeader = $headers['Authorization'] ?? '';
            
            if (strpos($authHeader, 'Bearer ') !== 0) {
                throw new AuthException('无效的认证令牌', 401);
            }
            
            $token = substr($authHeader, 7); // 移除 'Bearer ' 前缀
            
            // 解码token
            $decoded = base64_decode($token);
            $parts = explode(':', $decoded);
            
            if (count($parts) !== 3) {
                throw new AuthException('无效的认证令牌', 401);
            }
            
            [$userId, $timestamp, $random] = $parts;
            
            // 检查token是否过期（假设token有效期为24小时）
            if (time() - $timestamp > 24 * 60 * 60) {
                throw new AuthException('认证令牌已过期', 401);
            }
            
            // 查找用户
            $user = $this->userModel->getById($userId);
            
            if (!$user) {
                throw new AuthException('用户不存在', 401);
            }
            
            // 检查是否为管理员
            if ($user['role'] !== 'admin') {
                throw new AuthException('权限不足', 403);
            }
            
            // 设置响应头
            header('Content-Type: application/json; charset=UTF-8');
            http_response_code(200);
            
            return [
                'success' => true,
                'message' => '令牌有效',
                'user' => [
                    'id' => $user['id'],
                    'username' => $user['username'],
                    'email' => $user['email'],
                    'role' => $user['role']
                ]
            ];
        } catch (AuthException $e) {
            Logger::error("Token validation error: {$e->getMessage()}");
            http_response_code($e->getCode());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        } catch (\Exception $e) {
            Logger::error("Unexpected error during token validation: {$e->getMessage()}");
            http_response_code(500);
            return [
                'success' => false,
                'error' => '服务器内部错误'
            ];
        }
    }
}