^D:\PLB-LINKS\PLB-KJ\FRONTEND\WINDOWS\PLB_KJ_ADMIN\BUILD\WINDOWS\X64\CMAKEFILES\EBEC1568A24125CC71D8F4633964E2E6\FLUTTER_WINDOWS.DLL.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=D:\flutter PROJECT_DIR=D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin FLUTTER_ROOT=D:\flutter FLUTTER_EPHEMERAL_DIR=D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral PROJECT_DIR=D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin FLUTTER_TARGET=D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\lib\main.dart DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzUuMC0wLjEucHJl,RkxVVFRFUl9DSEFOTkVMPWJldGE=,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049YzBmMmExZGQ2MA==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049MWM5YzIwZTdjMw==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My45LjAgKGJ1aWxkIDMuOS4wLTMzMy4yLmJldGEp DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false PACKAGE_CONFIG=D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\.dart_tool\package_config.json D:/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\PLB-LINKS\PLB-KJ\FRONTEND\WINDOWS\PLB_KJ_ADMIN\BUILD\WINDOWS\X64\CMAKEFILES\8699CEF8AAF37F0AAA5326EDD5BE24D2\FLUTTER_ASSEMBLE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^D:\PLB-LINKS\PLB-KJ\FRONTEND\WINDOWS\PLB_KJ_ADMIN\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows -BD:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/build/windows/x64 --check-stamp-file D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
