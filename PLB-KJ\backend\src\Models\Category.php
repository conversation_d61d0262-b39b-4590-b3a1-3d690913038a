<?php

namespace App\Models;

class Category extends BaseModel
{
    protected $table = 'categories'; // 对应 plb_kj_categories 表
    
    /**
     * 获取所有分类（树形结构）
     *
     * @return array
     */
    public function getTree()
    {
        $categories = $this->findWhere(['status = ?'], [1]);
        return $this->buildTree($categories);
    }
    
    /**
     * 构建分类树
     *
     * @param array $categories
     * @param int $parentId
     * @return array
     */
    private function buildTree($categories, $parentId = 0)
    {
        $tree = [];
        
        foreach ($categories as $category) {
            if ($category['parent_id'] == $parentId) {
                $children = $this->buildTree($categories, $category['id']);
                if (!empty($children)) {
                    $category['children'] = $children;
                }
                $tree[] = $category;
            }
        }
        
        return $tree;
    }
    
    /**
     * 获取顶级分类
     *
     * @return array
     */
    public function getTopCategories()
    {
        return $this->findWhere(['parent_id = ? AND status = ?'], [0, 1]);
    }
    
    /**
     * 获取子分类
     *
     * @param int $parentId
     * @return array
     */
    public function getChildren($parentId)
    {
        return $this->findWhere(['parent_id = ? AND status = ?'], [$parentId, 1]);
    }
    
    /**
     * 获取分类路径
     *
     * @param int $categoryId
     * @return array
     */
    public function getCategoryPath($categoryId)
    {
        $path = [];
        $category = $this->findById($categoryId);
        
        while ($category) {
            array_unshift($path, $category);
            if ($category['parent_id'] == 0) {
                break;
            }
            $category = $this->findById($category['parent_id']);
        }
        
        return $path;
    }
    
    /**
     * 检查分类是否有子分类
     *
     * @param int $categoryId
     * @return bool
     */
    public function hasChildren($categoryId)
    {
        $count = $this->countWithConditions(['parent_id = ?'], [$categoryId]);
        return $count > 0;
    }
    
    /**
     * 获取分类下的商品数量
     *
     * @param int $categoryId
     * @return int
     */
    public function getProductCount($categoryId)
    {
        $sql = "SELECT COUNT(*) FROM {$this->prefix}products WHERE category_id = ? AND status = 'active'";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$categoryId]);
        
        return (int)$stmt->fetchColumn();
    }
}
