import 'package:flutter/material.dart';
import 'package:window_manager/window_manager.dart';
import 'package:provider/provider.dart';
import 'core/services/api_service.dart';
import 'core/services/customer_auth_service.dart';
import 'features/auth/screens/auth_selection_screen.dart';
import 'features/home/<USER>/home_screen.dart';
import 'features/chat/services/customer_chat_service.dart';
import 'features/chat/screens/customer_chat_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 配置窗口管理器
  await windowManager.ensureInitialized();

  WindowOptions windowOptions = const WindowOptions(
    size: Size(1200, 800),
    minimumSize: Size(1000, 600),
    center: true,
    backgroundColor: Colors.transparent,
    skipTaskbar: false,
    titleBarStyle: TitleBarStyle.normal,
    title: '跨境电商管理系统 - 用户端',
  );

  windowManager.waitUntilReadyToShow(windowOptions, () async {
    await windowManager.show();
    await windowManager.focus();
  });

  // 初始化服务
  await ApiService().initialize();
  await CustomerAuthService().initialize();

  runApp(const UserApp());
}

class UserApp extends StatelessWidget {
  const UserApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider.value(value: CustomerAuthService()),
        ChangeNotifierProvider(create: (_) => CustomerChatService()),
      ],
      child: MaterialApp(
        title: '跨境电商管理系统 - 用户端',
        theme: ThemeData(
          useMaterial3: true,
          colorScheme: ColorScheme.fromSeed(
            seedColor: const Color(0xFF2196F3),
            brightness: Brightness.light,
          ),
          primarySwatch: Colors.blue,
        ),
        debugShowCheckedModeBanner: false,
        home: Consumer<CustomerAuthService>(
          builder: (context, authService, child) {
            if (authService.isAuthenticated) {
              return const HomeScreen();
            } else {
              return const AuthSelectionScreen();
            }
          },
        ),
        routes: {
          '/auth': (context) => const AuthSelectionScreen(),
          '/home': (context) => const HomeScreen(),
          '/chat': (context) => const CustomerChatScreen(),
        },
      ),
    );
  }
}
