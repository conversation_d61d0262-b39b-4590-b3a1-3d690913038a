<?php

namespace App\Models;

class Order extends BaseModel
{
    protected $table = 'orders'; // 对应 plb_kj_orders 表
    
    /**
     * 创建订单
     *
     * @param array $orderData
     * @param array $items
     * @return int|false
     */
    public function createOrder($orderData, $items)
    {
        try {
            $this->db->beginTransaction();
            
            // 创建订单
            $orderId = $this->create($orderData);
            if (!$orderId) {
                throw new \Exception('创建订单失败');
            }
            
            // 创建订单项
            foreach ($items as $item) {
                $item['order_id'] = $orderId;
                $sql = "INSERT INTO {$this->prefix}order_items 
                        (order_id, product_id, product_variant_id, quantity, unit_price, total_price) 
                        VALUES (?, ?, ?, ?, ?, ?)";
                $stmt = $this->db->prepare($sql);
                $stmt->execute([
                    $item['order_id'],
                    $item['product_id'],
                    $item['product_variant_id'] ?? null,
                    $item['quantity'],
                    $item['unit_price'],
                    $item['total_price']
                ]);
            }
            
            $this->db->commit();
            return $orderId;
            
        } catch (\Exception $e) {
            $this->db->rollBack();
            return false;
        }
    }
    
    /**
     * 获取订单详情（包含订单项）
     *
     * @param int $id
     * @return array|null
     */
    public function getOrderDetails($id)
    {
        $order = $this->findById($id);
        if (!$order) {
            return null;
        }
        
        // 获取订单项
        $sql = "SELECT oi.*, p.name as product_name, p.sku as product_sku,
                       pv.variant_name, pv.variant_value
                FROM {$this->prefix}order_items oi
                LEFT JOIN {$this->prefix}products p ON oi.product_id = p.id
                LEFT JOIN {$this->prefix}product_variants pv ON oi.product_variant_id = pv.id
                WHERE oi.order_id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$id]);
        $order['items'] = $stmt->fetchAll(\PDO::FETCH_ASSOC);
        
        return $order;
    }
    
    /**
     * 根据客户ID获取订单
     *
     * @param int $customerId
     * @param int $page
     * @param int $pageSize
     * @return array
     */
    public function findByCustomer($customerId, $page = 1, $pageSize = 20)
    {
        $conditions = ['customer_id = ?'];
        $params = [$customerId];
        
        return $this->findAllWithPagination($page, $pageSize, $conditions, $params);
    }
    
    /**
     * 更新订单状态
     *
     * @param int $id
     * @param string $status
     * @return bool
     */
    public function updateStatus($id, $status)
    {
        return $this->update($id, ['status' => $status]);
    }
    
    /**
     * 获取订单统计
     *
     * @param string $date
     * @return array
     */
    public function getOrderStats($date = null)
    {
        if (!$date) {
            $date = date('Y-m-d');
        }
        
        $sql = "SELECT 
                    COUNT(*) as total_orders,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_orders,
                    SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing_orders,
                    SUM(CASE WHEN status = 'shipped' THEN 1 ELSE 0 END) as shipped_orders,
                    SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) as delivered_orders,
                    SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_orders,
                    SUM(total_amount) as total_revenue,
                    AVG(total_amount) as avg_order_value
                FROM {$this->table} 
                WHERE DATE(created_at) = ?";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$date]);
        
        return $stmt->fetch(\PDO::FETCH_ASSOC);
    }
    
    /**
     * 生成订单号
     *
     * @return string
     */
    public function generateOrderNumber()
    {
        return 'PLB' . date('Ymd') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
    }
}
