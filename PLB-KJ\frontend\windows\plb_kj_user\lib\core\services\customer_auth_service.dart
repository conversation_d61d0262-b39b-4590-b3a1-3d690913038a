import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/api_constants.dart';
import '../models/customer.dart';
import '../models/api_response.dart';
import 'api_service.dart';

/// 客户认证服务
class CustomerAuthService extends ChangeNotifier {
  static final CustomerAuthService _instance = CustomerAuthService._internal();
  factory CustomerAuthService() => _instance;
  CustomerAuthService._internal();

  final ApiService _apiService = ApiService();
  
  Customer? _currentCustomer;
  bool _isAuthenticated = false;
  bool _isLoading = false;

  /// 当前客户
  Customer? get currentCustomer => _currentCustomer;

  /// 是否已认证
  bool get isAuthenticated => _isAuthenticated;

  /// 是否正在加载
  bool get isLoading => _isLoading;

  /// 初始化认证服务
  Future<void> initialize() async {
    await _loadCustomerFromStorage();
  }

  /// 客户登录
  Future<ApiResponse<CustomerLoginResponse>> login({
    required String email,
    required String password,
  }) async {
    _setLoading(true);
    
    try {
      final request = CustomerLoginRequest(
        email: email,
        password: password,
      );

      final response = await _apiService.post<CustomerLoginResponse>(
        ApiConstants.customerLogin,
        data: request.toJson(),
        fromJson: (json) => CustomerLoginResponse.fromJson(json),
      );

      if (response.isSuccess && response.data != null) {
        final loginData = response.data!;
        
        // 保存token
        await _apiService.setAuthToken(loginData.token);
        
        // 保存客户信息
        _currentCustomer = loginData.customer;
        _isAuthenticated = true;
        
        // 持久化客户信息
        await _saveCustomerToStorage();
        
        notifyListeners();
      }

      return response;
    } finally {
      _setLoading(false);
    }
  }

  /// 客户注册
  Future<ApiResponse<Map<String, dynamic>>> register({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    String? username,
    String? phone,
    String? country,
    String? city,
    String? address,
    String? postalCode,
    String? languagePreference,
    String? currencyPreference,
  }) async {
    _setLoading(true);
    
    try {
      final request = CustomerRegisterRequest(
        email: email,
        password: password,
        firstName: firstName,
        lastName: lastName,
        username: username,
        phone: phone,
        country: country,
        city: city,
        address: address,
        postalCode: postalCode,
        languagePreference: languagePreference ?? AppConstants.defaultLanguage,
        currencyPreference: currencyPreference ?? AppConstants.defaultCurrency,
      );

      final response = await _apiService.post<Map<String, dynamic>>(
        ApiConstants.customerRegister,
        data: request.toJson(),
      );

      return response;
    } finally {
      _setLoading(false);
    }
  }

  /// 退出登录
  Future<void> logout() async {
    _setLoading(true);
    
    try {
      // 清除token
      await _apiService.clearAuthToken();
      
      // 清除客户信息
      _currentCustomer = null;
      _isAuthenticated = false;
      
      // 清除本地存储
      await _clearCustomerFromStorage();
      
      notifyListeners();
    } finally {
      _setLoading(false);
    }
  }

  /// 验证token有效性
  Future<bool> validateToken() async {
    if (!_apiService.isAuthenticated) {
      return false;
    }

    try {
      final response = await _apiService.get(ApiConstants.validateToken);
      
      if (response.isSuccess) {
        return true;
      } else {
        // Token无效，清除认证状态
        await logout();
        return false;
      }
    } catch (e) {
      // 验证失败，清除认证状态
      await logout();
      return false;
    }
  }

  /// 获取客户资料
  Future<ApiResponse<Customer>> getProfile() async {
    final response = await _apiService.get<Customer>(
      ApiConstants.customerProfile,
      fromJson: (json) => Customer.fromJson(json),
    );

    if (response.isSuccess && response.data != null) {
      _currentCustomer = response.data!;
      await _saveCustomerToStorage();
      notifyListeners();
    }

    return response;
  }

  /// 更新客户资料
  Future<ApiResponse<void>> updateProfile({
    String? username,
    String? firstName,
    String? lastName,
    String? phone,
    String? country,
    String? city,
    String? address,
    String? postalCode,
    String? languagePreference,
    String? currencyPreference,
    String? avatar,
  }) async {
    final data = <String, dynamic>{};
    
    if (username != null) data['username'] = username;
    if (firstName != null) data['first_name'] = firstName;
    if (lastName != null) data['last_name'] = lastName;
    if (phone != null) data['phone'] = phone;
    if (country != null) data['country'] = country;
    if (city != null) data['city'] = city;
    if (address != null) data['address'] = address;
    if (postalCode != null) data['postal_code'] = postalCode;
    if (languagePreference != null) data['language_preference'] = languagePreference;
    if (currencyPreference != null) data['currency_preference'] = currencyPreference;
    if (avatar != null) data['avatar'] = avatar;

    final response = await _apiService.put<void>(
      ApiConstants.customerProfile,
      data: data,
    );

    if (response.isSuccess) {
      // 重新获取最新的客户信息
      await getProfile();
    }

    return response;
  }

  /// 修改密码
  Future<ApiResponse<void>> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    final response = await _apiService.put<void>(
      ApiConstants.customerPassword,
      data: {
        'current_password': currentPassword,
        'new_password': newPassword,
      },
    );

    return response;
  }

  /// 更新客户信息
  void updateCustomer(Customer customer) {
    _currentCustomer = customer;
    _saveCustomerToStorage();
    notifyListeners();
  }

  /// 设置加载状态
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// 从本地存储加载客户信息
  Future<void> _loadCustomerFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final customerJson = prefs.getString(AppConstants.storageKeyCurrentUser);
      
      if (customerJson != null && _apiService.isAuthenticated) {
        // 这里需要实现JSON字符串到Map的转换
        // 由于SharedPreferences只能存储字符串，我们需要使用JSON编码
        // 暂时跳过，在实际实现中需要使用dart:convert
        _isAuthenticated = true;
        notifyListeners();
      }
    } catch (e) {
      print('加载客户信息失败: $e');
    }
  }

  /// 保存客户信息到本地存储
  Future<void> _saveCustomerToStorage() async {
    if (_currentCustomer != null) {
      try {
        final prefs = await SharedPreferences.getInstance();
        // 这里需要将Customer对象转换为JSON字符串
        // 暂时跳过，在实际实现中需要使用dart:convert
        await prefs.setString(AppConstants.storageKeyCurrentUser, 'customer_data');
      } catch (e) {
        print('保存客户信息失败: $e');
      }
    }
  }

  /// 清除本地存储的客户信息
  Future<void> _clearCustomerFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(AppConstants.storageKeyCurrentUser);
    } catch (e) {
      print('清除客户信息失败: $e');
    }
  }
}
