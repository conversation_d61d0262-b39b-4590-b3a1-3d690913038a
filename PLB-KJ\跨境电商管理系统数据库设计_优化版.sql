-- 跨境电商管理系统数据库设计 - 优化版
-- 增加实时通信聊天功能和用户端支持

-- 1. 系统用户表（管理端用户）
CREATE TABLE IF NOT EXISTS plb_kj_admin_users (
  id INT PRIMARY KEY AUTO_INCREMENT,
  username VARCHAR(50) NOT NULL UNIQUE,
  email VARCHAR(100) NOT NULL UNIQUE,
  password_hash VARCHAR(255) NOT NULL,
  first_name VA<PERSON>HA<PERSON>(50),
  last_name VA<PERSON>HAR(50),
  phone VARCHAR(20),
  avatar VARCHAR(255),
  role ENUM('admin', 'manager', 'staff', 'customer_service') DEFAULT 'staff',
  department VARCHAR(50),
  is_online TINYINT DEFAULT 0,
  last_login_at TIMESTAMP NULL,
  status TINYINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 2. 客户表（用户端用户）
CREATE TABLE IF NOT EXISTS plb_kj_customers (
  id INT PRIMARY KEY AUTO_INCREMENT,
  username VARCHAR(50) UNIQUE,
  email VARCHAR(100) NOT NULL UNIQUE,
  password_hash VARCHAR(255) NOT NULL,
  first_name VARCHAR(50) NOT NULL,
  last_name VARCHAR(50) NOT NULL,
  phone VARCHAR(20),
  country VARCHAR(50),
  city VARCHAR(50),
  address TEXT,
  postal_code VARCHAR(20),
  avatar VARCHAR(255),
  language_preference VARCHAR(10) DEFAULT 'zh-CN',
  currency_preference VARCHAR(3) DEFAULT 'USD',
  is_online TINYINT DEFAULT 0,
  last_login_at TIMESTAMP NULL,
  email_verified TINYINT DEFAULT 0,
  email_verified_at TIMESTAMP NULL,
  status TINYINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 3. 聊天会话表
CREATE TABLE IF NOT EXISTS plb_kj_chat_sessions (
  id INT PRIMARY KEY AUTO_INCREMENT,
  session_id VARCHAR(100) NOT NULL UNIQUE,
  customer_id INT NOT NULL,
  admin_user_id INT NULL,
  title VARCHAR(200) DEFAULT '客服咨询',
  status ENUM('waiting', 'active', 'closed', 'transferred') DEFAULT 'waiting',
  priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
  category VARCHAR(50) DEFAULT 'general',
  started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  ended_at TIMESTAMP NULL,
  last_message_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  customer_satisfaction_rating TINYINT NULL,
  customer_feedback TEXT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (customer_id) REFERENCES plb_kj_customers(id),
  FOREIGN KEY (admin_user_id) REFERENCES plb_kj_admin_users(id),
  INDEX idx_customer_id (customer_id),
  INDEX idx_admin_user_id (admin_user_id),
  INDEX idx_status (status),
  INDEX idx_last_message_at (last_message_at)
);

-- 4. 聊天消息表
CREATE TABLE IF NOT EXISTS plb_kj_chat_messages (
  id INT PRIMARY KEY AUTO_INCREMENT,
  session_id INT NOT NULL,
  sender_type ENUM('customer', 'admin', 'system') NOT NULL,
  sender_id INT NOT NULL,
  message_type ENUM('text', 'image', 'file', 'system', 'quick_reply') DEFAULT 'text',
  content TEXT NOT NULL,
  file_url VARCHAR(500) NULL,
  file_name VARCHAR(255) NULL,
  file_size INT NULL,
  is_read TINYINT DEFAULT 0,
  read_at TIMESTAMP NULL,
  is_deleted TINYINT DEFAULT 0,
  reply_to_message_id INT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (session_id) REFERENCES plb_kj_chat_sessions(id) ON DELETE CASCADE,
  FOREIGN KEY (reply_to_message_id) REFERENCES plb_kj_chat_messages(id),
  INDEX idx_session_id (session_id),
  INDEX idx_sender (sender_type, sender_id),
  INDEX idx_created_at (created_at),
  INDEX idx_is_read (is_read)
);

-- 5. 快捷回复模板表
CREATE TABLE IF NOT EXISTS plb_kj_chat_quick_replies (
  id INT PRIMARY KEY AUTO_INCREMENT,
  category VARCHAR(50) NOT NULL,
  title VARCHAR(100) NOT NULL,
  content TEXT NOT NULL,
  sort_order INT DEFAULT 0,
  is_active TINYINT DEFAULT 1,
  created_by INT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (created_by) REFERENCES plb_kj_admin_users(id),
  INDEX idx_category (category),
  INDEX idx_is_active (is_active)
);

-- 6. 在线状态表
CREATE TABLE IF NOT EXISTS plb_kj_online_status (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_type ENUM('customer', 'admin') NOT NULL,
  user_id INT NOT NULL,
  socket_id VARCHAR(100),
  last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY unique_user (user_type, user_id),
  INDEX idx_last_seen (last_seen)
);

-- 7. 通知表
CREATE TABLE IF NOT EXISTS plb_kj_notifications (
  id INT PRIMARY KEY AUTO_INCREMENT,
  recipient_type ENUM('customer', 'admin') NOT NULL,
  recipient_id INT NOT NULL,
  type VARCHAR(50) NOT NULL,
  title VARCHAR(200) NOT NULL,
  content TEXT,
  data JSON NULL,
  is_read TINYINT DEFAULT 0,
  read_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (recipient_id) REFERENCES plb_kj_customers(id) ON DELETE CASCADE,
  INDEX idx_recipient (recipient_type, recipient_id),
  INDEX idx_is_read (is_read),
  INDEX idx_created_at (created_at)
);

-- 8. 商品分类表（优化）
CREATE TABLE IF NOT EXISTS plb_kj_categories (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(100) NOT NULL,
  name_en VARCHAR(100),
  description TEXT,
  description_en TEXT,
  parent_id INT DEFAULT 0,
  level TINYINT DEFAULT 1,
  path VARCHAR(500),
  sort_order INT DEFAULT 0,
  image VARCHAR(255),
  seo_title VARCHAR(200),
  seo_description TEXT,
  status TINYINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_parent_id (parent_id),
  INDEX idx_status (status),
  INDEX idx_sort_order (sort_order)
);

-- 9. 商品表（优化）
CREATE TABLE IF NOT EXISTS plb_kj_products (
  id INT PRIMARY KEY AUTO_INCREMENT,
  name VARCHAR(200) NOT NULL,
  name_en VARCHAR(200),
  description TEXT,
  description_en TEXT,
  short_description TEXT,
  short_description_en TEXT,
  sku VARCHAR(100) NOT NULL UNIQUE,
  category_id INT,
  brand VARCHAR(100),
  price DECIMAL(10, 2) NOT NULL,
  compare_price DECIMAL(10, 2),
  cost DECIMAL(10, 2) DEFAULT 0,
  weight DECIMAL(8, 2),
  dimensions VARCHAR(50),
  tags VARCHAR(500),
  status ENUM('draft', 'active', 'inactive', 'out_of_stock') DEFAULT 'draft',
  is_featured TINYINT DEFAULT 0,
  is_digital TINYINT DEFAULT 0,
  requires_shipping TINYINT DEFAULT 1,
  track_inventory TINYINT DEFAULT 1,
  inventory_quantity INT DEFAULT 0,
  min_stock_level INT DEFAULT 0,
  seo_title VARCHAR(200),
  seo_description TEXT,
  view_count INT DEFAULT 0,
  sales_count INT DEFAULT 0,
  rating_average DECIMAL(3, 2) DEFAULT 0,
  rating_count INT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (category_id) REFERENCES plb_kj_categories(id),
  INDEX idx_category_id (category_id),
  INDEX idx_status (status),
  INDEX idx_is_featured (is_featured),
  INDEX idx_price (price),
  FULLTEXT idx_search (name, description, tags)
);

-- 10. 商品图片表（优化）
CREATE TABLE IF NOT EXISTS plb_kj_product_images (
  id INT PRIMARY KEY AUTO_INCREMENT,
  product_id INT NOT NULL,
  image_url VARCHAR(255) NOT NULL,
  thumbnail_url VARCHAR(255),
  alt_text VARCHAR(200),
  sort_order INT DEFAULT 0,
  is_primary TINYINT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (product_id) REFERENCES plb_kj_products(id) ON DELETE CASCADE,
  INDEX idx_product_id (product_id),
  INDEX idx_sort_order (sort_order)
);

-- 11. 商品变体表
CREATE TABLE IF NOT EXISTS plb_kj_product_variants (
  id INT PRIMARY KEY AUTO_INCREMENT,
  product_id INT NOT NULL,
  sku VARCHAR(100) NOT NULL UNIQUE,
  title VARCHAR(200),
  price DECIMAL(10, 2),
  compare_price DECIMAL(10, 2),
  cost DECIMAL(10, 2),
  weight DECIMAL(8, 2),
  inventory_quantity INT DEFAULT 0,
  option1_name VARCHAR(50),
  option1_value VARCHAR(100),
  option2_name VARCHAR(50),
  option2_value VARCHAR(100),
  option3_name VARCHAR(50),
  option3_value VARCHAR(100),
  image_url VARCHAR(255),
  is_active TINYINT DEFAULT 1,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (product_id) REFERENCES plb_kj_products(id) ON DELETE CASCADE,
  INDEX idx_product_id (product_id),
  INDEX idx_is_active (is_active)
);

-- 12. 货币表（优化）
CREATE TABLE IF NOT EXISTS plb_kj_currencies (
  id INT PRIMARY KEY AUTO_INCREMENT,
  code VARCHAR(3) NOT NULL UNIQUE,
  name VARCHAR(50) NOT NULL,
  symbol VARCHAR(10) NOT NULL,
  rate DECIMAL(10, 6) NOT NULL,
  is_default TINYINT DEFAULT 0,
  is_active TINYINT DEFAULT 1,
  last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_is_default (is_default),
  INDEX idx_is_active (is_active)
);

-- 13. 订单表（优化）
CREATE TABLE IF NOT EXISTS plb_kj_orders (
  id INT PRIMARY KEY AUTO_INCREMENT,
  order_number VARCHAR(50) NOT NULL UNIQUE,
  customer_id INT NOT NULL,
  currency_id INT NOT NULL,
  subtotal DECIMAL(10, 2) NOT NULL,
  tax_amount DECIMAL(10, 2) DEFAULT 0,
  shipping_amount DECIMAL(10, 2) DEFAULT 0,
  discount_amount DECIMAL(10, 2) DEFAULT 0,
  total_amount DECIMAL(10, 2) NOT NULL,
  status ENUM('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded') DEFAULT 'pending',
  payment_status ENUM('pending', 'paid', 'failed', 'refunded', 'partially_refunded') DEFAULT 'pending',
  fulfillment_status ENUM('unfulfilled', 'partial', 'fulfilled') DEFAULT 'unfulfilled',
  shipping_address JSON,
  billing_address JSON,
  customer_note TEXT,
  admin_note TEXT,
  tags VARCHAR(500),
  source VARCHAR(50) DEFAULT 'web',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (customer_id) REFERENCES plb_kj_customers(id),
  FOREIGN KEY (currency_id) REFERENCES plb_kj_currencies(id),
  INDEX idx_customer_id (customer_id),
  INDEX idx_status (status),
  INDEX idx_payment_status (payment_status),
  INDEX idx_created_at (created_at)
);

-- 插入默认管理员用户
INSERT IGNORE INTO plb_kj_admin_users (username, email, password_hash, first_name, last_name, role, status) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '系统', '管理员', 'admin', 1),
('manager', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '业务', '经理', 'manager', 1),
('service1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '客服', '一号', 'customer_service', 1);

-- 插入默认货币
INSERT IGNORE INTO plb_kj_currencies (code, name, symbol, rate, is_default, is_active) VALUES
('USD', 'US Dollar', '$', 1.000000, 1, 1),
('EUR', 'Euro', '€', 0.850000, 0, 1),
('GBP', 'British Pound', '£', 0.750000, 0, 1),
('CNY', 'Chinese Yuan', '¥', 7.200000, 0, 1),
('JPY', 'Japanese Yen', '¥', 110.000000, 0, 1);

-- 插入默认商品分类
INSERT IGNORE INTO plb_kj_categories (name, name_en, description, description_en, parent_id, level, path, sort_order) VALUES
('电子产品', 'Electronics', '各类电子产品', 'Various electronic products', 0, 1, '1', 1),
('服装配饰', 'Fashion', '服装和配饰', 'Clothing and accessories', 0, 1, '2', 2),
('家居用品', 'Home & Garden', '家居和园艺用品', 'Home and garden products', 0, 1, '3', 3),
('运动户外', 'Sports & Outdoors', '运动和户外用品', 'Sports and outdoor products', 0, 1, '4', 4),
('美妆护肤', 'Beauty & Personal Care', '美妆和个人护理', 'Beauty and personal care', 0, 1, '5', 5);

-- 插入子分类
INSERT IGNORE INTO plb_kj_categories (name, name_en, description, description_en, parent_id, level, path, sort_order) VALUES
('手机数码', 'Mobile & Digital', '手机和数码产品', 'Mobile phones and digital products', 1, 2, '1/6', 1),
('电脑办公', 'Computers & Office', '电脑和办公用品', 'Computers and office supplies', 1, 2, '1/7', 2),
('男装', 'Men\'s Clothing', '男士服装', 'Men\'s clothing', 2, 2, '2/8', 1),
('女装', 'Women\'s Clothing', '女士服装', 'Women\'s clothing', 2, 2, '2/9', 2);

-- 插入快捷回复模板
INSERT IGNORE INTO plb_kj_chat_quick_replies (category, title, content, sort_order, created_by) VALUES
('greeting', '欢迎语', '您好！欢迎来到我们的在线客服，我是您的专属客服，有什么可以帮助您的吗？', 1, 1),
('greeting', '工作时间', '我们的客服工作时间是周一至周日 9:00-21:00，如果您在非工作时间咨询，我们会在工作时间内尽快回复您。', 2, 1),
('order', '订单查询', '请提供您的订单号，我来帮您查询订单状态。', 1, 1),
('order', '订单修改', '很抱歉，订单一旦确认支付后暂时无法修改，如需修改请联系我们的客服人员。', 2, 1),
('shipping', '物流查询', '请提供您的订单号或快递单号，我来帮您查询物流信息。', 1, 1),
('shipping', '发货时间', '我们会在您付款后1-3个工作日内发货，具体发货时间请以实际为准。', 2, 1),
('return', '退换货政策', '我们支持7天无理由退换货，商品需保持原包装和吊牌完整。具体退换货流程请查看我们的退换货政策。', 1, 1),
('payment', '支付问题', '我们支持多种支付方式：信用卡、PayPal、银行转账等。如果支付遇到问题，请截图发给我。', 1, 1),
('closing', '结束语', '感谢您的咨询，如果还有其他问题，随时联系我们。祝您购物愉快！', 1, 1);

-- 插入系统设置
INSERT IGNORE INTO plb_kj_system_settings (setting_key, setting_value, setting_type, category, description, is_public) VALUES
('site_name', '跨境电商管理系统', 'string', 'general', '网站名称', 1),
('site_email', '<EMAIL>', 'string', 'general', '网站邮箱', 0),
('currency_default', 'USD', 'string', 'general', '默认货币', 1),
('tax_rate', '0.00', 'number', 'general', '默认税率', 1),
('chat_enabled', '1', 'boolean', 'chat', '是否启用聊天功能', 1),
('chat_auto_assign', '1', 'boolean', 'chat', '是否自动分配客服', 0),
('chat_max_sessions_per_agent', '10', 'number', 'chat', '每个客服最大同时会话数', 0),
('chat_session_timeout', '1800', 'number', 'chat', '会话超时时间（秒）', 0),
('notification_email_enabled', '1', 'boolean', 'notification', '是否启用邮件通知', 0),
('notification_sms_enabled', '0', 'boolean', 'notification', '是否启用短信通知', 0);

-- 创建视图：活跃聊天会话
CREATE VIEW plb_kj_active_chat_sessions AS
SELECT
  cs.*,
  c.first_name as customer_first_name,
  c.last_name as customer_last_name,
  c.email as customer_email,
  c.avatar as customer_avatar,
  c.is_online as customer_is_online,
  au.first_name as admin_first_name,
  au.last_name as admin_last_name,
  au.avatar as admin_avatar,
  au.is_online as admin_is_online,
  (SELECT COUNT(*) FROM plb_kj_chat_messages cm WHERE cm.session_id = cs.id AND cm.is_read = 0 AND cm.sender_type = 'customer') as unread_customer_messages,
  (SELECT COUNT(*) FROM plb_kj_chat_messages cm WHERE cm.session_id = cs.id AND cm.is_read = 0 AND cm.sender_type = 'admin') as unread_admin_messages
FROM plb_kj_chat_sessions cs
LEFT JOIN plb_kj_customers c ON cs.customer_id = c.id
LEFT JOIN plb_kj_admin_users au ON cs.admin_user_id = au.id
WHERE cs.status IN ('waiting', 'active');

-- 创建视图：客服工作统计
CREATE VIEW plb_kj_agent_stats AS
SELECT
  au.id,
  au.username,
  au.first_name,
  au.last_name,
  au.is_online,
  COUNT(CASE WHEN cs.status = 'active' THEN 1 END) as active_sessions,
  COUNT(CASE WHEN cs.status = 'waiting' THEN 1 END) as waiting_sessions,
  COUNT(CASE WHEN DATE(cs.created_at) = CURDATE() THEN 1 END) as today_sessions,
  AVG(cs.customer_satisfaction_rating) as avg_rating
FROM plb_kj_admin_users au
LEFT JOIN plb_kj_chat_sessions cs ON au.id = cs.admin_user_id
WHERE au.role = 'customer_service' AND au.status = 1
GROUP BY au.id;

-- 创建存储过程：自动分配客服
DELIMITER //
CREATE PROCEDURE AssignCustomerService(IN session_id INT)
BEGIN
  DECLARE agent_id INT DEFAULT NULL;

  -- 查找在线且会话数最少的客服
  SELECT au.id INTO agent_id
  FROM plb_kj_admin_users au
  LEFT JOIN plb_kj_chat_sessions cs ON au.id = cs.admin_user_id AND cs.status = 'active'
  WHERE au.role = 'customer_service'
    AND au.status = 1
    AND au.is_online = 1
  GROUP BY au.id
  HAVING COUNT(cs.id) < (
    SELECT setting_value
    FROM plb_kj_system_settings
    WHERE setting_key = 'chat_max_sessions_per_agent'
  )
  ORDER BY COUNT(cs.id) ASC, RAND()
  LIMIT 1;

  -- 如果找到可用客服，分配会话
  IF agent_id IS NOT NULL THEN
    UPDATE plb_kj_chat_sessions
    SET admin_user_id = agent_id, status = 'active', updated_at = NOW()
    WHERE id = session_id;
  END IF;
END //
DELIMITER ;

-- 创建触发器：更新最后消息时间
DELIMITER //
CREATE TRIGGER update_session_last_message
AFTER INSERT ON plb_kj_chat_messages
FOR EACH ROW
BEGIN
  UPDATE plb_kj_chat_sessions
  SET last_message_at = NEW.created_at, updated_at = NOW()
  WHERE id = NEW.session_id;
END //
DELIMITER ;

-- 14. 订单商品表（优化）
CREATE TABLE IF NOT EXISTS plb_kj_order_items (
  id INT PRIMARY KEY AUTO_INCREMENT,
  order_id INT NOT NULL,
  product_id INT NOT NULL,
  variant_id INT NULL,
  quantity INT NOT NULL,
  unit_price DECIMAL(10, 2) NOT NULL,
  total_price DECIMAL(10, 2) NOT NULL,
  product_title VARCHAR(200),
  variant_title VARCHAR(200),
  sku VARCHAR(100),
  weight DECIMAL(8, 2),
  requires_shipping TINYINT DEFAULT 1,
  fulfillment_status ENUM('unfulfilled', 'fulfilled', 'cancelled') DEFAULT 'unfulfilled',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (order_id) REFERENCES plb_kj_orders(id) ON DELETE CASCADE,
  FOREIGN KEY (product_id) REFERENCES plb_kj_products(id),
  FOREIGN KEY (variant_id) REFERENCES plb_kj_product_variants(id),
  INDEX idx_order_id (order_id),
  INDEX idx_product_id (product_id)
);

-- 15. 购物车表（优化）
CREATE TABLE IF NOT EXISTS plb_kj_shopping_cart (
  id INT PRIMARY KEY AUTO_INCREMENT,
  customer_id INT NOT NULL,
  product_id INT NOT NULL,
  variant_id INT NULL,
  quantity INT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (customer_id) REFERENCES plb_kj_customers(id) ON DELETE CASCADE,
  FOREIGN KEY (product_id) REFERENCES plb_kj_products(id) ON DELETE CASCADE,
  FOREIGN KEY (variant_id) REFERENCES plb_kj_product_variants(id) ON DELETE CASCADE,
  UNIQUE KEY unique_customer_product_variant (customer_id, product_id, variant_id),
  INDEX idx_customer_id (customer_id)
);

-- 16. 客户收藏表
CREATE TABLE IF NOT EXISTS plb_kj_customer_favorites (
  id INT PRIMARY KEY AUTO_INCREMENT,
  customer_id INT NOT NULL,
  product_id INT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (customer_id) REFERENCES plb_kj_customers(id) ON DELETE CASCADE,
  FOREIGN KEY (product_id) REFERENCES plb_kj_products(id) ON DELETE CASCADE,
  UNIQUE KEY unique_customer_product (customer_id, product_id),
  INDEX idx_customer_id (customer_id)
);

-- 17. 客户地址表
CREATE TABLE IF NOT EXISTS plb_kj_customer_addresses (
  id INT PRIMARY KEY AUTO_INCREMENT,
  customer_id INT NOT NULL,
  type ENUM('shipping', 'billing', 'both') DEFAULT 'shipping',
  first_name VARCHAR(50) NOT NULL,
  last_name VARCHAR(50) NOT NULL,
  company VARCHAR(100),
  address1 VARCHAR(255) NOT NULL,
  address2 VARCHAR(255),
  city VARCHAR(100) NOT NULL,
  province VARCHAR(100),
  country VARCHAR(50) NOT NULL,
  postal_code VARCHAR(20),
  phone VARCHAR(20),
  is_default TINYINT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (customer_id) REFERENCES plb_kj_customers(id) ON DELETE CASCADE,
  INDEX idx_customer_id (customer_id),
  INDEX idx_is_default (is_default)
);

-- 18. 系统设置表（优化）
CREATE TABLE IF NOT EXISTS plb_kj_system_settings (
  id INT PRIMARY KEY AUTO_INCREMENT,
  setting_key VARCHAR(100) NOT NULL UNIQUE,
  setting_value TEXT,
  setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
  category VARCHAR(50) DEFAULT 'general',
  description VARCHAR(255),
  is_public TINYINT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_category (category),
  INDEX idx_is_public (is_public)
);

-- 19. 操作日志表
CREATE TABLE IF NOT EXISTS plb_kj_activity_logs (
  id INT PRIMARY KEY AUTO_INCREMENT,
  user_type ENUM('customer', 'admin') NOT NULL,
  user_id INT NOT NULL,
  action VARCHAR(100) NOT NULL,
  description TEXT,
  model_type VARCHAR(100),
  model_id INT,
  old_values JSON,
  new_values JSON,
  ip_address VARCHAR(45),
  user_agent TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_user (user_type, user_id),
  INDEX idx_action (action),
  INDEX idx_created_at (created_at)
);

-- 20. 文件上传表
CREATE TABLE IF NOT EXISTS plb_kj_file_uploads (
  id INT PRIMARY KEY AUTO_INCREMENT,
  original_name VARCHAR(255) NOT NULL,
  file_name VARCHAR(255) NOT NULL,
  file_path VARCHAR(500) NOT NULL,
  file_size INT NOT NULL,
  mime_type VARCHAR(100) NOT NULL,
  file_type ENUM('image', 'document', 'video', 'audio', 'other') NOT NULL,
  uploaded_by_type ENUM('customer', 'admin') NOT NULL,
  uploaded_by_id INT NOT NULL,
  is_public TINYINT DEFAULT 0,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_uploaded_by (uploaded_by_type, uploaded_by_id),
  INDEX idx_file_type (file_type),
  INDEX idx_created_at (created_at)
);
