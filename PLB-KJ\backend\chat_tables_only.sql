-- PLB-KJ 聊天功能数据库表（仅聊天相关表）
-- 如果主数据库已存在，只需要导入聊天相关的表

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 聊天会话表
CREATE TABLE IF NOT EXISTS `plb_kj_chat_sessions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` varchar(36) NOT NULL COMMENT '会话唯一标识符',
  `customer_id` int(11) NOT NULL COMMENT '客户ID',
  `admin_user_id` int(11) DEFAULT NULL COMMENT '客服ID',
  `title` varchar(255) NOT NULL DEFAULT '客服咨询' COMMENT '会话标题',
  `status` enum('waiting','active','closed') NOT NULL DEFAULT 'waiting' COMMENT '会话状态',
  `priority` enum('low','normal','high','urgent') NOT NULL DEFAULT 'normal' COMMENT '优先级',
  `category` enum('general','technical','billing','complaint') NOT NULL DEFAULT 'general' COMMENT '分类',
  `started_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
  `ended_at` timestamp NULL DEFAULT NULL COMMENT '结束时间',
  `last_message_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后消息时间',
  `customer_satisfaction_rating` tinyint(1) DEFAULT NULL COMMENT '客户满意度评分(1-5)',
  `customer_feedback` text DEFAULT NULL COMMENT '客户反馈',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `session_id` (`session_id`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_admin_user_id` (`admin_user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_priority` (`priority`),
  KEY `idx_started_at` (`started_at`),
  KEY `idx_last_message_at` (`last_message_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天会话表';

-- 聊天消息表
CREATE TABLE IF NOT EXISTS `plb_kj_chat_messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` int(11) NOT NULL COMMENT '会话ID',
  `sender_type` enum('customer','admin') NOT NULL COMMENT '发送者类型',
  `sender_id` int(11) NOT NULL COMMENT '发送者ID',
  `message_type` enum('text','image','file','audio') NOT NULL DEFAULT 'text' COMMENT '消息类型',
  `content` text NOT NULL COMMENT '消息内容',
  `reply_to_message_id` int(11) DEFAULT NULL COMMENT '回复的消息ID',
  `is_read` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已读',
  `read_at` timestamp NULL DEFAULT NULL COMMENT '已读时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_sender` (`sender_type`, `sender_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_is_read` (`is_read`),
  KEY `idx_reply_to_message_id` (`reply_to_message_id`),
  FOREIGN KEY (`session_id`) REFERENCES `plb_kj_chat_sessions` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`reply_to_message_id`) REFERENCES `plb_kj_chat_messages` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天消息表';

-- 在线状态表
CREATE TABLE IF NOT EXISTS `plb_kj_online_status` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_type` enum('customer','admin') NOT NULL COMMENT '用户类型',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `socket_id` varchar(255) NOT NULL COMMENT 'Socket连接ID',
  `last_seen` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后在线时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user` (`user_type`, `user_id`),
  KEY `idx_socket_id` (`socket_id`),
  KEY `idx_last_seen` (`last_seen`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='在线状态表';

-- 聊天文件表
CREATE TABLE IF NOT EXISTS `plb_kj_chat_files` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `message_id` int(11) NOT NULL COMMENT '消息ID',
  `original_name` varchar(255) NOT NULL COMMENT '原始文件名',
  `file_name` varchar(255) NOT NULL COMMENT '存储文件名',
  `file_path` varchar(500) NOT NULL COMMENT '文件路径',
  `file_size` bigint(20) NOT NULL COMMENT '文件大小(字节)',
  `mime_type` varchar(100) NOT NULL COMMENT 'MIME类型',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_message_id` (`message_id`),
  FOREIGN KEY (`message_id`) REFERENCES `plb_kj_chat_messages` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天文件表';

-- 聊天统计表
CREATE TABLE IF NOT EXISTS `plb_kj_chat_stats` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `date` date NOT NULL COMMENT '统计日期',
  `total_sessions` int(11) NOT NULL DEFAULT 0 COMMENT '总会话数',
  `active_sessions` int(11) NOT NULL DEFAULT 0 COMMENT '活跃会话数',
  `closed_sessions` int(11) NOT NULL DEFAULT 0 COMMENT '已关闭会话数',
  `total_messages` int(11) NOT NULL DEFAULT 0 COMMENT '总消息数',
  `average_response_time` int(11) NOT NULL DEFAULT 0 COMMENT '平均响应时间(秒)',
  `average_session_duration` int(11) NOT NULL DEFAULT 0 COMMENT '平均会话时长(秒)',
  `customer_satisfaction_avg` decimal(3,2) DEFAULT NULL COMMENT '客户满意度平均分',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_date` (`date`),
  KEY `idx_date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天统计表';

-- 为现有用户表添加在线状态字段（如果不存在）
ALTER TABLE `plb_kj_customers` 
ADD COLUMN IF NOT EXISTS `is_online` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否在线' AFTER `status`,
ADD COLUMN IF NOT EXISTS `last_login_at` timestamp NULL DEFAULT NULL COMMENT '最后登录时间' AFTER `is_online`;

ALTER TABLE `plb_kj_admin_users` 
ADD COLUMN IF NOT EXISTS `is_online` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否在线' AFTER `status`,
ADD COLUMN IF NOT EXISTS `last_login_at` timestamp NULL DEFAULT NULL COMMENT '最后登录时间' AFTER `is_online`;

-- 添加索引（如果不存在）
ALTER TABLE `plb_kj_customers` ADD INDEX IF NOT EXISTS `idx_is_online` (`is_online`);
ALTER TABLE `plb_kj_admin_users` ADD INDEX IF NOT EXISTS `idx_is_online` (`is_online`);

-- 插入一些示例数据
INSERT IGNORE INTO `plb_kj_chat_sessions` (`session_id`, `customer_id`, `admin_user_id`, `title`, `status`, `priority`, `category`, `started_at`, `last_message_at`) VALUES
('550e8400-e29b-41d4-a716-446655440001', 1, 1, '产品咨询', 'active', 'normal', 'general', NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440002', 2, NULL, '技术支持', 'waiting', 'high', 'technical', NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440003', 3, 1, '账单问题', 'closed', 'normal', 'billing', DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY));

INSERT IGNORE INTO `plb_kj_chat_messages` (`session_id`, `sender_type`, `sender_id`, `message_type`, `content`, `is_read`) VALUES
(1, 'customer', 1, 'text', '你好，我想咨询一下产品信息', 1),
(1, 'admin', 1, 'text', '您好！很高兴为您服务，请问您想了解哪款产品呢？', 1),
(1, 'customer', 1, 'text', '我想了解最新的手机产品', 0),
(2, 'customer', 2, 'text', '我的订单有问题，需要技术支持', 0),
(3, 'customer', 3, 'text', '我的账单金额不对', 1),
(3, 'admin', 1, 'text', '我来帮您查看一下账单详情', 1);

-- 创建视图：会话详情视图
CREATE OR REPLACE VIEW `plb_kj_chat_session_details` AS
SELECT 
    s.id,
    s.session_id,
    s.customer_id,
    s.admin_user_id,
    s.title,
    s.status,
    s.priority,
    s.category,
    s.started_at,
    s.ended_at,
    s.last_message_at,
    s.customer_satisfaction_rating,
    s.customer_feedback,
    c.first_name as customer_first_name,
    c.last_name as customer_last_name,
    c.email as customer_email,
    c.avatar as customer_avatar,
    COALESCE(c.is_online, 0) as customer_is_online,
    a.first_name as admin_first_name,
    a.last_name as admin_last_name,
    a.email as admin_email,
    a.avatar as admin_avatar,
    COALESCE(a.is_online, 0) as admin_is_online,
    (SELECT COUNT(*) FROM plb_kj_chat_messages m WHERE m.session_id = s.id AND m.is_read = 0 AND m.sender_type != 'admin') as unread_count
FROM plb_kj_chat_sessions s
LEFT JOIN plb_kj_customers c ON s.customer_id = c.id
LEFT JOIN plb_kj_admin_users a ON s.admin_user_id = a.id;

-- 创建存储过程：更新会话统计
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS UpdateChatStats(IN stat_date DATE)
BEGIN
    INSERT INTO plb_kj_chat_stats (
        date, 
        total_sessions, 
        active_sessions, 
        closed_sessions, 
        total_messages,
        average_response_time,
        average_session_duration,
        customer_satisfaction_avg
    ) VALUES (
        stat_date,
        (SELECT COUNT(*) FROM plb_kj_chat_sessions WHERE DATE(started_at) = stat_date),
        (SELECT COUNT(*) FROM plb_kj_chat_sessions WHERE DATE(started_at) = stat_date AND status = 'active'),
        (SELECT COUNT(*) FROM plb_kj_chat_sessions WHERE DATE(started_at) = stat_date AND status = 'closed'),
        (SELECT COUNT(*) FROM plb_kj_chat_messages m 
         JOIN plb_kj_chat_sessions s ON m.session_id = s.id 
         WHERE DATE(m.created_at) = stat_date),
        0, -- 平均响应时间需要复杂计算
        (SELECT AVG(TIMESTAMPDIFF(SECOND, started_at, ended_at)) 
         FROM plb_kj_chat_sessions 
         WHERE DATE(started_at) = stat_date AND ended_at IS NOT NULL),
        (SELECT AVG(customer_satisfaction_rating) 
         FROM plb_kj_chat_sessions 
         WHERE DATE(started_at) = stat_date AND customer_satisfaction_rating IS NOT NULL)
    ) ON DUPLICATE KEY UPDATE
        total_sessions = VALUES(total_sessions),
        active_sessions = VALUES(active_sessions),
        closed_sessions = VALUES(closed_sessions),
        total_messages = VALUES(total_messages),
        average_session_duration = VALUES(average_session_duration),
        customer_satisfaction_avg = VALUES(customer_satisfaction_avg),
        updated_at = CURRENT_TIMESTAMP;
END //
DELIMITER ;

-- 创建触发器：自动更新最后消息时间
DROP TRIGGER IF EXISTS update_last_message_time;
DELIMITER //
CREATE TRIGGER update_last_message_time 
AFTER INSERT ON plb_kj_chat_messages
FOR EACH ROW
BEGIN
    UPDATE plb_kj_chat_sessions 
    SET last_message_at = NEW.created_at 
    WHERE id = NEW.session_id;
END //
DELIMITER ;

SET FOREIGN_KEY_CHECKS = 1;
