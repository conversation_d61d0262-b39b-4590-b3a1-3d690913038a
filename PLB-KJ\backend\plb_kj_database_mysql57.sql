-- PLB-KJ 跨境电商管理系统数据库设计 - MySQL 5.7 兼容版本
-- 解决MySQL 5.7的兼容性问题

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;
SET sql_mode = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION';

-- 1. 系统用户表（管理端用户）
CREATE TABLE IF NOT EXISTS `plb_kj_admin_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `first_name` varchar(50) DEFAULT NULL,
  `last_name` varchar(50) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `avatar` varchar(255) DEFAULT NULL,
  `role` enum('admin','manager','staff','customer_service') NOT NULL DEFAULT 'staff',
  `department` varchar(50) DEFAULT NULL,
  `is_online` tinyint(1) NOT NULL DEFAULT '0',
  `last_login_at` timestamp NULL DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  KEY `idx_role` (`role`),
  KEY `idx_status` (`status`),
  KEY `idx_is_online` (`is_online`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员用户表';

-- 2. 客户表（用户端用户）
CREATE TABLE IF NOT EXISTS `plb_kj_customers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) DEFAULT NULL,
  `email` varchar(100) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `first_name` varchar(50) NOT NULL,
  `last_name` varchar(50) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `country` varchar(50) DEFAULT NULL,
  `city` varchar(50) DEFAULT NULL,
  `address` text,
  `postal_code` varchar(20) DEFAULT NULL,
  `avatar` varchar(255) DEFAULT NULL,
  `language_preference` varchar(10) NOT NULL DEFAULT 'zh-CN',
  `currency_preference` varchar(3) NOT NULL DEFAULT 'USD',
  `is_online` tinyint(1) NOT NULL DEFAULT '0',
  `last_login_at` timestamp NULL DEFAULT NULL,
  `email_verified` tinyint(1) NOT NULL DEFAULT '0',
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  KEY `idx_status` (`status`),
  KEY `idx_is_online` (`is_online`),
  KEY `idx_email_verified` (`email_verified`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户表';

-- 3. 聊天会话表
CREATE TABLE IF NOT EXISTS `plb_kj_chat_sessions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` varchar(36) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `admin_user_id` int(11) DEFAULT NULL,
  `title` varchar(255) NOT NULL DEFAULT '客服咨询',
  `status` enum('waiting','active','closed') NOT NULL DEFAULT 'waiting',
  `priority` enum('low','normal','high','urgent') NOT NULL DEFAULT 'normal',
  `category` enum('general','technical','billing','complaint') NOT NULL DEFAULT 'general',
  `started_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `ended_at` timestamp NULL DEFAULT NULL,
  `last_message_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `customer_satisfaction_rating` tinyint(1) DEFAULT NULL,
  `customer_feedback` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `session_id` (`session_id`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_admin_user_id` (`admin_user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_last_message_at` (`last_message_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天会话表';

-- 4. 聊天消息表
CREATE TABLE IF NOT EXISTS `plb_kj_chat_messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` int(11) NOT NULL,
  `sender_type` enum('customer','admin','system') NOT NULL,
  `sender_id` int(11) NOT NULL,
  `message_type` enum('text','image','file','system','quick_reply') NOT NULL DEFAULT 'text',
  `content` text NOT NULL,
  `sender_name` varchar(100) NOT NULL,
  `sender_avatar` varchar(255) DEFAULT NULL,
  `is_read` tinyint(1) NOT NULL DEFAULT '0',
  `read_at` timestamp NULL DEFAULT NULL,
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0',
  `reply_to_message_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_sender` (`sender_type`,`sender_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_is_read` (`is_read`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天消息表';

-- 5. 快捷回复模板表
CREATE TABLE IF NOT EXISTS `plb_kj_chat_quick_replies` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category` varchar(50) NOT NULL,
  `title` varchar(100) NOT NULL,
  `content` text NOT NULL,
  `sort_order` int(11) NOT NULL DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_category` (`category`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='快捷回复模板表';

-- 6. 在线状态表
CREATE TABLE IF NOT EXISTS `plb_kj_online_status` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_type` enum('customer','admin') NOT NULL,
  `user_id` int(11) NOT NULL,
  `socket_id` varchar(100) DEFAULT NULL,
  `last_seen` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user` (`user_type`,`user_id`),
  KEY `idx_last_seen` (`last_seen`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='在线状态表';

-- 7. 通知表
CREATE TABLE IF NOT EXISTS `plb_kj_notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `recipient_type` enum('customer','admin') NOT NULL,
  `recipient_id` int(11) NOT NULL,
  `type` varchar(50) NOT NULL,
  `title` varchar(200) NOT NULL,
  `content` text,
  `data` text COMMENT 'JSON格式数据',
  `is_read` tinyint(1) NOT NULL DEFAULT '0',
  `read_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_recipient` (`recipient_type`,`recipient_id`),
  KEY `idx_is_read` (`is_read`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知表';

-- 8. 商品分类表
CREATE TABLE IF NOT EXISTS `plb_kj_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `name_en` varchar(100) DEFAULT NULL,
  `description` text,
  `description_en` text,
  `parent_id` int(11) NOT NULL DEFAULT '0',
  `level` tinyint(1) NOT NULL DEFAULT '1',
  `path` varchar(500) DEFAULT NULL,
  `sort_order` int(11) NOT NULL DEFAULT '0',
  `image` varchar(255) DEFAULT NULL,
  `seo_title` varchar(200) DEFAULT NULL,
  `seo_description` text,
  `status` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品分类表';

-- 9. 货币表
CREATE TABLE IF NOT EXISTS `plb_kj_currencies` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(3) NOT NULL,
  `name` varchar(50) NOT NULL,
  `symbol` varchar(10) NOT NULL,
  `rate` decimal(10,6) NOT NULL,
  `is_default` tinyint(1) NOT NULL DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`),
  KEY `idx_is_default` (`is_default`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='货币表';

-- 10. 系统设置表
CREATE TABLE IF NOT EXISTS `plb_kj_system_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text,
  `setting_type` enum('string','number','boolean','json') NOT NULL DEFAULT 'string',
  `category` varchar(50) NOT NULL DEFAULT 'general',
  `description` varchar(255) DEFAULT NULL,
  `is_public` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`),
  KEY `idx_category` (`category`),
  KEY `idx_is_public` (`is_public`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统设置表';

-- 添加外键约束（分开执行避免循环依赖）
ALTER TABLE `plb_kj_chat_sessions`
ADD CONSTRAINT `fk_chat_sessions_customer` FOREIGN KEY (`customer_id`) REFERENCES `plb_kj_customers` (`id`) ON DELETE CASCADE,
ADD CONSTRAINT `fk_chat_sessions_admin` FOREIGN KEY (`admin_user_id`) REFERENCES `plb_kj_admin_users` (`id`) ON DELETE SET NULL;

ALTER TABLE `plb_kj_chat_messages`
ADD CONSTRAINT `fk_chat_messages_session` FOREIGN KEY (`session_id`) REFERENCES `plb_kj_chat_sessions` (`id`) ON DELETE CASCADE,
ADD CONSTRAINT `fk_chat_messages_reply` FOREIGN KEY (`reply_to_message_id`) REFERENCES `plb_kj_chat_messages` (`id`) ON DELETE SET NULL;

ALTER TABLE `plb_kj_chat_quick_replies`
ADD CONSTRAINT `fk_quick_replies_creator` FOREIGN KEY (`created_by`) REFERENCES `plb_kj_admin_users` (`id`) ON DELETE CASCADE;

-- 插入默认管理员用户
INSERT IGNORE INTO `plb_kj_admin_users` (`username`, `email`, `password_hash`, `first_name`, `last_name`, `role`, `status`) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '系统', '管理员', 'admin', 1),
('manager', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '业务', '经理', 'manager', 1),
('service1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '客服', '小王', 'customer_service', 1);

-- 插入默认货币
INSERT IGNORE INTO `plb_kj_currencies` (`code`, `name`, `symbol`, `rate`, `is_default`, `is_active`) VALUES
('USD', 'US Dollar', '$', 1.000000, 1, 1),
('EUR', 'Euro', '€', 0.850000, 0, 1),
('GBP', 'British Pound', '£', 0.750000, 0, 1),
('CNY', 'Chinese Yuan', '¥', 7.200000, 0, 1);

-- 插入默认商品分类
INSERT IGNORE INTO `plb_kj_categories` (`name`, `name_en`, `description`, `description_en`, `parent_id`, `level`, `path`, `sort_order`) VALUES
('电子产品', 'Electronics', '各类电子产品', 'Various electronic products', 0, 1, '1', 1),
('服装配饰', 'Fashion', '时尚服装和配饰', 'Fashion clothing and accessories', 0, 1, '2', 2),
('家居用品', 'Home & Garden', '家居装饰和园艺用品', 'Home decoration and garden supplies', 0, 1, '3', 3),
('运动户外', 'Sports & Outdoors', '运动和户外用品', 'Sports and outdoor equipment', 0, 1, '4', 4),
('美妆护肤', 'Beauty & Personal Care', '美容和个人护理产品', 'Beauty and personal care products', 0, 1, '5', 5);

-- 插入子分类
INSERT IGNORE INTO `plb_kj_categories` (`name`, `name_en`, `description`, `description_en`, `parent_id`, `level`, `path`, `sort_order`) VALUES
('手机数码', 'Mobile & Digital', '手机和数码产品', 'Mobile phones and digital products', 1, 2, '1/6', 1),
('电脑办公', 'Computers & Office', '电脑和办公设备', 'Computers and office equipment', 1, 2, '1/7', 2),
('男装', 'Men\'s Clothing', '男士服装', 'Men\'s clothing', 2, 2, '2/8', 1),
('女装', 'Women\'s Clothing', '女士服装', 'Women\'s clothing', 2, 2, '2/9', 2);

-- 插入快捷回复模板
INSERT IGNORE INTO `plb_kj_chat_quick_replies` (`category`, `title`, `content`, `sort_order`, `created_by`) VALUES
('greeting', '欢迎语', '您好！欢迎来到我们的在线客服，我是您的专属客服，有什么可以帮助您的吗？', 1, 1),
('common', '稍等回复', '好的，请稍等，我马上为您查询处理。', 2, 1),
('common', '感谢回复', '感谢您的耐心等待，如果还有其他问题，请随时联系我们。', 3, 1),
('technical', '技术支持', '我来为您解决技术问题，请详细描述您遇到的情况。', 4, 1),
('billing', '账单查询', '我来帮您查看账单详情，请提供您的订单号或账户信息。', 5, 1),
('shipping', '物流查询', '我来帮您查询物流信息，请提供您的订单号。', 6, 1),
('return', '退换货政策', '我们支持7天无理由退换货，商品需保持原包装和吊牌完整。具体退换货流程请查看我们的退换货政策。', 7, 1),
('payment', '支付问题', '我们支持多种支付方式：信用卡、PayPal、银行转账等。如果支付遇到问题，请截图发给我。', 8, 1),
('closing', '结束语', '感谢您的咨询，如果还有其他问题，随时联系我们。祝您购物愉快！', 9, 1);

-- 插入系统设置
INSERT IGNORE INTO `plb_kj_system_settings` (`setting_key`, `setting_value`, `setting_type`, `category`, `description`, `is_public`) VALUES
('site_name', 'PLB-KJ跨境电商管理系统', 'string', 'general', '网站名称', 1),
('site_email', '<EMAIL>', 'string', 'general', '网站邮箱', 0),
('currency_default', 'USD', 'string', 'general', '默认货币', 1),
('tax_rate', '0.00', 'number', 'general', '默认税率', 1),
('chat_enabled', '1', 'boolean', 'chat', '是否启用聊天功能', 1),
('chat_max_sessions_per_agent', '5', 'number', 'chat', '每个客服最大并发会话数', 0),
('chat_auto_assign', '1', 'boolean', 'chat', '是否自动分配客服', 0),
('chat_session_timeout', '1800', 'number', 'chat', '会话超时时间(秒)', 0),
('chat_file_upload_enabled', '1', 'boolean', 'chat', '是否允许文件上传', 0),
('chat_file_max_size', '10485760', 'number', 'chat', '文件上传最大大小(字节)', 0),
('chat_allowed_file_types', 'jpg,jpeg,png,gif,pdf,doc,docx,txt', 'string', 'chat', '允许的文件类型', 0),
('mail_driver', 'smtp', 'string', 'mail', '邮件驱动', 0),
('mail_host', 'smtp.gmail.com', 'string', 'mail', 'SMTP主机', 0),
('mail_port', '587', 'number', 'mail', 'SMTP端口', 0),
('security_password_min_length', '8', 'number', 'security', '密码最小长度', 0),
('api_rate_limit', '1000', 'number', 'api', 'API请求频率限制(每小时)', 0);

-- 插入示例客户数据
INSERT IGNORE INTO `plb_kj_customers` (`username`, `email`, `password_hash`, `first_name`, `last_name`, `phone`, `country`, `status`) VALUES
('customer1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '张', '三', '+86-13800138001', 'China', 1),
('customer2', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '李', '四', '+86-13800138002', 'China', 1),
('customer3', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'John', 'Smith', '******-0123', 'USA', 1);

-- 插入示例聊天会话
INSERT IGNORE INTO `plb_kj_chat_sessions` (`session_id`, `customer_id`, `admin_user_id`, `title`, `status`, `priority`, `category`, `started_at`, `last_message_at`) VALUES
('550e8400-e29b-41d4-a716-446655440001', 1, 3, '产品咨询', 'active', 'normal', 'general', NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440002', 2, NULL, '技术支持', 'waiting', 'high', 'technical', NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440003', 3, 3, '账单问题', 'closed', 'normal', 'billing', DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY));

-- 插入示例聊天消息
INSERT IGNORE INTO `plb_kj_chat_messages` (`session_id`, `sender_type`, `sender_id`, `sender_name`, `message_type`, `content`, `is_read`) VALUES
(1, 'customer', 1, '张三', 'text', '你好，我想咨询一下产品信息', 1),
(1, 'admin', 3, '客服小王', 'text', '您好！很高兴为您服务，请问您想了解哪款产品呢？', 1),
(1, 'customer', 1, '张三', 'text', '我想了解最新的手机产品', 0),
(2, 'customer', 2, '李四', 'text', '我的订单有问题，需要技术支持', 0),
(3, 'customer', 3, 'John Smith', 'text', '我的账单金额不对', 1),
(3, 'admin', 3, '客服小王', 'text', '我来帮您查看一下账单详情', 1);

-- 创建视图：会话详情视图
CREATE OR REPLACE VIEW `plb_kj_chat_session_details` AS
SELECT
    s.id,
    s.session_id,
    s.customer_id,
    s.admin_user_id,
    s.title,
    s.status,
    s.priority,
    s.category,
    s.started_at,
    s.ended_at,
    s.last_message_at,
    s.customer_satisfaction_rating,
    s.customer_feedback,
    COALESCE(c.first_name, '') as customer_first_name,
    COALESCE(c.last_name, '') as customer_last_name,
    COALESCE(c.email, '') as customer_email,
    COALESCE(c.avatar, '') as customer_avatar,
    COALESCE(c.is_online, 0) as customer_is_online,
    COALESCE(a.first_name, '') as admin_first_name,
    COALESCE(a.last_name, '') as admin_last_name,
    COALESCE(a.email, '') as admin_email,
    COALESCE(a.avatar, '') as admin_avatar,
    COALESCE(a.is_online, 0) as admin_is_online,
    (SELECT COUNT(*) FROM plb_kj_chat_messages m WHERE m.session_id = s.id AND m.is_read = 0 AND m.sender_type = 'customer') as unread_count
FROM plb_kj_chat_sessions s
LEFT JOIN plb_kj_customers c ON s.customer_id = c.id
LEFT JOIN plb_kj_admin_users a ON s.admin_user_id = a.id;

-- 创建触发器：自动更新最后消息时间
DROP TRIGGER IF EXISTS update_last_message_time;
DELIMITER //
CREATE TRIGGER update_last_message_time
AFTER INSERT ON plb_kj_chat_messages
FOR EACH ROW
BEGIN
    UPDATE plb_kj_chat_sessions
    SET last_message_at = NEW.created_at
    WHERE id = NEW.session_id;
END //
DELIMITER ;

SET FOREIGN_KEY_CHECKS = 1;

-- 验证安装结果
SELECT 'PLB-KJ Database Setup Completed Successfully!' as status;
SELECT 'Admin Users' as table_name, COUNT(*) as count FROM plb_kj_admin_users
UNION ALL
SELECT 'Customers' as table_name, COUNT(*) as count FROM plb_kj_customers
UNION ALL
SELECT 'Chat Sessions' as table_name, COUNT(*) as count FROM plb_kj_chat_sessions
UNION ALL
SELECT 'Chat Messages' as table_name, COUNT(*) as count FROM plb_kj_chat_messages
UNION ALL
SELECT 'Quick Replies' as table_name, COUNT(*) as count FROM plb_kj_chat_quick_replies
UNION ALL
SELECT 'System Settings' as table_name, COUNT(*) as count FROM plb_kj_system_settings
UNION ALL
SELECT 'Categories' as table_name, COUNT(*) as count FROM plb_kj_categories
UNION ALL
SELECT 'Currencies' as table_name, COUNT(*) as count FROM plb_kj_currencies;
