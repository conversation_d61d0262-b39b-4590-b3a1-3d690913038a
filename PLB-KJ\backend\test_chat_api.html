<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PLB-KJ 聊天API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea, button {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 100%;
            box-sizing: border-box;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            margin: 5px 0;
        }
        button:hover {
            background: #0056b3;
        }
        .response {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .flex {
            display: flex;
            gap: 20px;
        }
        .flex > div {
            flex: 1;
        }
        .session-item {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            margin: 5px 0;
            cursor: pointer;
        }
        .session-item:hover {
            background: #f8f9fa;
        }
        .session-item.selected {
            background: #e7f3ff;
            border-color: #007bff;
        }
        .message-item {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px;
            margin: 3px 0;
            font-size: 12px;
        }
        .message-sent {
            background: #e3f2fd;
            margin-left: 20px;
        }
        .message-received {
            background: #f5f5f5;
            margin-right: 20px;
        }
    </style>
</head>
<body>
    <h1>PLB-KJ 聊天API测试工具</h1>

    <!-- 登录区域 -->
    <div class="container">
        <h3>用户登录</h3>
        <div class="flex">
            <div>
                <div class="form-group">
                    <label>用户类型:</label>
                    <select id="userType">
                        <option value="admin">管理员</option>
                        <option value="customer">客户</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>用户ID:</label>
                    <input type="number" id="userId" value="1" min="1">
                </div>
                <button onclick="login()">登录获取Token</button>
            </div>
            <div>
                <div class="form-group">
                    <label>当前Token:</label>
                    <textarea id="currentToken" rows="3" readonly></textarea>
                </div>
                <div class="form-group">
                    <label>API基础URL:</label>
                    <input type="text" id="baseUrl" value="http://localhost/PLB-KJ/backend/public/api">
                </div>
            </div>
        </div>
    </div>

    <!-- 会话管理 -->
    <div class="flex">
        <div class="container">
            <h3>会话管理</h3>
            <button onclick="getSessions()">获取会话列表</button>
            <button onclick="createSession()">创建新会话</button>
            <button onclick="getChatStats()">获取聊天统计</button>
            
            <div class="form-group">
                <label>会话标题:</label>
                <input type="text" id="sessionTitle" value="测试会话">
            </div>
            <div class="form-group">
                <label>分类:</label>
                <select id="sessionCategory">
                    <option value="general">一般咨询</option>
                    <option value="technical">技术支持</option>
                    <option value="billing">账单问题</option>
                    <option value="complaint">投诉建议</option>
                </select>
            </div>
            <div class="form-group">
                <label>优先级:</label>
                <select id="sessionPriority">
                    <option value="normal">普通</option>
                    <option value="high">高</option>
                    <option value="urgent">紧急</option>
                </select>
            </div>

            <div id="sessionsList"></div>
        </div>

        <div class="container">
            <h3>消息管理</h3>
            <div class="form-group">
                <label>选中的会话ID:</label>
                <input type="number" id="selectedSessionId" readonly>
            </div>
            
            <button onclick="getMessages()">获取消息</button>
            <button onclick="acceptSession()">接受会话</button>
            <button onclick="closeSession()">关闭会话</button>
            
            <div class="form-group">
                <label>消息内容:</label>
                <textarea id="messageContent" rows="3" placeholder="输入消息内容..."></textarea>
            </div>
            <div class="form-group">
                <label>消息类型:</label>
                <select id="messageType">
                    <option value="text">文本</option>
                    <option value="image">图片</option>
                    <option value="file">文件</option>
                </select>
            </div>
            <button onclick="sendMessage()">发送消息</button>

            <div id="messagesList"></div>
        </div>
    </div>

    <!-- 响应区域 -->
    <div class="container">
        <h3>API响应</h3>
        <div id="response" class="response"></div>
    </div>

    <script>
        let currentToken = '';
        let selectedSessionId = null;

        // 登录获取Token
        async function login() {
            const userType = document.getElementById('userType').value;
            const userId = document.getElementById('userId').value;
            const baseUrl = document.getElementById('baseUrl').value;

            try {
                const endpoint = userType === 'admin' ? '/admin/login' : '/users/login';
                const response = await fetch(`${baseUrl}${endpoint}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: userType === 'admin' ? '<EMAIL>' : '<EMAIL>',
                        password: 'password123'
                    })
                });

                const data = await response.json();
                
                if (data.success && data.data.token) {
                    currentToken = data.data.token;
                    document.getElementById('currentToken').value = currentToken;
                    showResponse('登录成功', data, true);
                } else {
                    showResponse('登录失败', data, false);
                }
            } catch (error) {
                showResponse('登录错误', { error: error.message }, false);
            }
        }

        // 获取会话列表
        async function getSessions() {
            const response = await apiRequest('/chat/sessions', 'GET');
            if (response.success) {
                displaySessions(response.data.sessions);
            }
        }

        // 创建会话
        async function createSession() {
            const title = document.getElementById('sessionTitle').value;
            const category = document.getElementById('sessionCategory').value;
            const priority = document.getElementById('sessionPriority').value;

            const response = await apiRequest('/chat/sessions', 'POST', {
                title: title,
                category: category,
                priority: priority
            });

            if (response.success) {
                getSessions(); // 刷新会话列表
            }
        }

        // 获取消息
        async function getMessages() {
            if (!selectedSessionId) {
                alert('请先选择一个会话');
                return;
            }

            const response = await apiRequest(`/chat/sessions/${selectedSessionId}/messages`, 'GET');
            if (response.success) {
                displayMessages(response.data.messages);
            }
        }

        // 发送消息
        async function sendMessage() {
            if (!selectedSessionId) {
                alert('请先选择一个会话');
                return;
            }

            const content = document.getElementById('messageContent').value;
            const messageType = document.getElementById('messageType').value;

            if (!content.trim()) {
                alert('请输入消息内容');
                return;
            }

            const response = await apiRequest(`/chat/sessions/${selectedSessionId}/messages`, 'POST', {
                content: content,
                message_type: messageType
            });

            if (response.success) {
                document.getElementById('messageContent').value = '';
                getMessages(); // 刷新消息列表
            }
        }

        // 接受会话
        async function acceptSession() {
            if (!selectedSessionId) {
                alert('请先选择一个会话');
                return;
            }

            const response = await apiRequest(`/chat/sessions/${selectedSessionId}/accept`, 'PUT');
            if (response.success) {
                getSessions(); // 刷新会话列表
            }
        }

        // 关闭会话
        async function closeSession() {
            if (!selectedSessionId) {
                alert('请先选择一个会话');
                return;
            }

            const response = await apiRequest(`/chat/sessions/${selectedSessionId}/close`, 'PUT', {
                rating: 5,
                feedback: '测试关闭'
            });

            if (response.success) {
                getSessions(); // 刷新会话列表
            }
        }

        // 获取聊天统计
        async function getChatStats() {
            const response = await apiRequest('/chat/stats', 'GET');
        }

        // 通用API请求函数
        async function apiRequest(endpoint, method = 'GET', data = null) {
            const baseUrl = document.getElementById('baseUrl').value;
            
            if (!currentToken) {
                alert('请先登录获取Token');
                return { success: false };
            }

            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${currentToken}`
                    }
                };

                if (data && (method === 'POST' || method === 'PUT')) {
                    options.body = JSON.stringify(data);
                }

                const response = await fetch(`${baseUrl}${endpoint}`, options);
                const result = await response.json();
                
                showResponse(`${method} ${endpoint}`, result, result.success);
                return result;
            } catch (error) {
                const errorResult = { success: false, error: error.message };
                showResponse(`${method} ${endpoint} - 错误`, errorResult, false);
                return errorResult;
            }
        }

        // 显示会话列表
        function displaySessions(sessions) {
            const container = document.getElementById('sessionsList');
            container.innerHTML = '<h4>会话列表:</h4>';
            
            sessions.forEach(session => {
                const div = document.createElement('div');
                div.className = 'session-item';
                div.onclick = () => selectSession(session.id);
                div.innerHTML = `
                    <strong>${session.title}</strong> (${session.status})<br>
                    <small>客户: ${session.customer.first_name} ${session.customer.last_name}</small><br>
                    <small>时间: ${session.last_message_at}</small>
                `;
                container.appendChild(div);
            });
        }

        // 显示消息列表
        function displayMessages(messages) {
            const container = document.getElementById('messagesList');
            container.innerHTML = '<h4>消息列表:</h4>';
            
            messages.forEach(message => {
                const div = document.createElement('div');
                div.className = `message-item ${message.sender_type === 'admin' ? 'message-sent' : 'message-received'}`;
                div.innerHTML = `
                    <strong>${message.sender_name}</strong> (${message.sender_type})<br>
                    ${message.content}<br>
                    <small>${message.created_at}</small>
                `;
                container.appendChild(div);
            });
        }

        // 选择会话
        function selectSession(sessionId) {
            selectedSessionId = sessionId;
            document.getElementById('selectedSessionId').value = sessionId;
            
            // 更新选中状态
            document.querySelectorAll('.session-item').forEach(item => {
                item.classList.remove('selected');
            });
            event.target.closest('.session-item').classList.add('selected');
            
            // 自动获取消息
            getMessages();
        }

        // 显示响应
        function showResponse(title, data, success) {
            const responseDiv = document.getElementById('response');
            responseDiv.className = `response ${success ? 'success' : 'error'}`;
            responseDiv.textContent = `${title}:\n${JSON.stringify(data, null, 2)}`;
        }
    </script>
</body>
</html>
