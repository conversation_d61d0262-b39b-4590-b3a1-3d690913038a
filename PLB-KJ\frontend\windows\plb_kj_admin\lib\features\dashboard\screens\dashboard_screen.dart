import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/services/auth_service.dart';

class DashboardScreen extends StatelessWidget {
  const DashboardScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('管理员控制台'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          Consumer<AuthService>(
            builder: (context, authService, child) {
              return PopupMenuButton<String>(
                icon: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircleAvatar(
                      radius: 16,
                      backgroundColor: Colors.white,
                      child: Text(
                        authService.currentUser?.displayName.substring(0, 1).toUpperCase() ?? 'A',
                        style: TextStyle(
                          color: Theme.of(context).primaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      authService.currentUser?.displayName ?? '管理员',
                      style: const TextStyle(fontSize: 14),
                    ),
                    const Icon(Icons.arrow_drop_down),
                  ],
                ),
                onSelected: (value) async {
                  switch (value) {
                    case 'profile':
                      // TODO: 打开个人资料页面
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('个人资料功能开发中...')),
                      );
                      break;
                    case 'settings':
                      // TODO: 打开设置页面
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('设置功能开发中...')),
                      );
                      break;
                    case 'logout':
                      await authService.logout();
                      break;
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'profile',
                    child: ListTile(
                      leading: Icon(Icons.person),
                      title: Text('个人资料'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'settings',
                    child: ListTile(
                      leading: Icon(Icons.settings),
                      title: Text('设置'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  const PopupMenuDivider(),
                  const PopupMenuItem(
                    value: 'logout',
                    child: ListTile(
                      leading: Icon(Icons.logout, color: Colors.red),
                      title: Text('退出登录', style: TextStyle(color: Colors.red)),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                ],
              );
            },
          ),
          const SizedBox(width: 16),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Consumer<AuthService>(
              builder: (context, authService, child) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '欢迎回来，${authService.currentUser?.displayName ?? '管理员'}！',
                      style: const TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '角色：${authService.currentUser?.roleDisplayName ?? '未知'}',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                );
              },
            ),
            const SizedBox(height: 8),
            Text(
              '选择下方功能模块开始管理',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 32),

            // 功能模块网格
            Expanded(
              child: Consumer<AuthService>(
                builder: (context, authService, child) {
                  return GridView.count(
                    crossAxisCount: 3,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                    children: [
                      if (authService.hasPermission('chat_service'))
                        _buildFunctionCard(
                          context,
                          icon: Icons.chat_bubble,
                          title: '客服聊天',
                          subtitle: '管理客户咨询',
                          color: Colors.blue,
                          onTap: () => Navigator.pushNamed(context, '/chat'),
                        ),
                      if (authService.hasPermission('user_management'))
                        _buildFunctionCard(
                          context,
                          icon: Icons.people,
                          title: '用户管理',
                          subtitle: '管理系统用户',
                          color: Colors.green,
                          onTap: () {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(content: Text('用户管理功能开发中...')),
                            );
                          },
                        ),
                      if (authService.hasPermission('product_management'))
                        _buildFunctionCard(
                          context,
                          icon: Icons.inventory,
                          title: '商品管理',
                          subtitle: '管理商品信息',
                          color: Colors.orange,
                          onTap: () {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(content: Text('商品管理功能开发中...')),
                            );
                          },
                        ),
                      if (authService.hasPermission('order_management'))
                        _buildFunctionCard(
                          context,
                          icon: Icons.shopping_cart,
                          title: '订单管理',
                          subtitle: '处理订单信息',
                          color: Colors.purple,
                          onTap: () {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(content: Text('订单管理功能开发中...')),
                            );
                          },
                        ),
                      _buildFunctionCard(
                        context,
                        icon: Icons.analytics,
                        title: '数据统计',
                        subtitle: '查看业务数据',
                        color: Colors.teal,
                        onTap: () {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('数据统计功能开发中...')),
                          );
                        },
                      ),
                      if (authService.hasPermission('system_settings'))
                        _buildFunctionCard(
                          context,
                          icon: Icons.settings,
                          title: '系统设置',
                          subtitle: '配置系统参数',
                          color: Colors.grey,
                          onTap: () {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(content: Text('系统设置功能开发中...')),
                            );
                          },
                        ),
                    ],
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFunctionCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 4,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  size: 32,
                  color: color,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
