import 'package:flutter/foundation.dart';

// 聊天会话模型
class ChatSession {
  final int id;
  final String sessionId;
  final int customerId;
  final int? adminUserId;
  final String title;
  final String status; // waiting, active, closed
  final String priority; // low, normal, high, urgent
  final String category; // general, technical, billing, complaint
  final DateTime startedAt;
  final DateTime? endedAt;
  final DateTime lastMessageAt;
  final int? customerSatisfactionRating;
  final String? customerFeedback;
  final CustomerInfo? customer;
  final AdminInfo? admin;
  final int unreadCount;

  ChatSession({
    required this.id,
    required this.sessionId,
    required this.customerId,
    this.adminUserId,
    required this.title,
    required this.status,
    required this.priority,
    required this.category,
    required this.startedAt,
    this.endedAt,
    required this.lastMessageAt,
    this.customerSatisfactionRating,
    this.customerFeedback,
    this.customer,
    this.admin,
    this.unreadCount = 0,
  });

  factory ChatSession.fromJson(Map<String, dynamic> json) {
    return ChatSession(
      id: json['id'] ?? 0,
      sessionId: json['session_id'] ?? json['sessionId'] ?? '',
      customerId: json['customer_id'] ?? json['customerId'] ?? 0,
      adminUserId: json['admin_user_id'] ?? json['adminUserId'],
      title: json['title'] ?? '客服咨询',
      status: json['status'] ?? 'waiting',
      priority: json['priority'] ?? 'normal',
      category: json['category'] ?? 'general',
      startedAt: DateTime.parse(json['started_at'] ?? json['startedAt'] ?? DateTime.now().toIso8601String()),
      endedAt: json['ended_at'] != null || json['endedAt'] != null 
          ? DateTime.parse(json['ended_at'] ?? json['endedAt']) 
          : null,
      lastMessageAt: DateTime.parse(json['last_message_at'] ?? json['lastMessageAt'] ?? DateTime.now().toIso8601String()),
      customerSatisfactionRating: json['customer_satisfaction_rating'] ?? json['customerSatisfactionRating'],
      customerFeedback: json['customer_feedback'] ?? json['customerFeedback'],
      customer: json['customer'] != null ? CustomerInfo.fromJson(json['customer']) : null,
      admin: json['admin'] != null ? AdminInfo.fromJson(json['admin']) : null,
      unreadCount: json['unread_count'] ?? json['unreadCount'] ?? 0,
    );
  }

  ChatSession copyWith({
    int? id,
    String? sessionId,
    int? customerId,
    int? adminUserId,
    String? title,
    String? status,
    String? priority,
    String? category,
    DateTime? startedAt,
    DateTime? endedAt,
    DateTime? lastMessageAt,
    int? customerSatisfactionRating,
    String? customerFeedback,
    CustomerInfo? customer,
    AdminInfo? admin,
    int? unreadCount,
  }) {
    return ChatSession(
      id: id ?? this.id,
      sessionId: sessionId ?? this.sessionId,
      customerId: customerId ?? this.customerId,
      adminUserId: adminUserId ?? this.adminUserId,
      title: title ?? this.title,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      category: category ?? this.category,
      startedAt: startedAt ?? this.startedAt,
      endedAt: endedAt ?? this.endedAt,
      lastMessageAt: lastMessageAt ?? this.lastMessageAt,
      customerSatisfactionRating: customerSatisfactionRating ?? this.customerSatisfactionRating,
      customerFeedback: customerFeedback ?? this.customerFeedback,
      customer: customer ?? this.customer,
      admin: admin ?? this.admin,
      unreadCount: unreadCount ?? this.unreadCount,
    );
  }
}

// 聊天消息模型
class ChatMessage {
  final int id;
  final int sessionId;
  final String senderType; // customer, admin
  final int senderId;
  final String senderName;
  final String? senderAvatar;
  final String messageType; // text, image, file, audio
  final String content;
  final int? replyToMessageId;
  bool isRead;
  final DateTime createdAt;
  final DateTime? readAt;

  ChatMessage({
    required this.id,
    required this.sessionId,
    required this.senderType,
    required this.senderId,
    required this.senderName,
    this.senderAvatar,
    required this.messageType,
    required this.content,
    this.replyToMessageId,
    this.isRead = false,
    required this.createdAt,
    this.readAt,
  });

  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      id: json['id'] ?? 0,
      sessionId: json['session_id'] ?? json['sessionId'] ?? 0,
      senderType: json['sender_type'] ?? json['senderType'] ?? 'customer',
      senderId: json['sender_id'] ?? json['senderId'] ?? 0,
      senderName: json['sender_name'] ?? json['senderName'] ?? '未知用户',
      senderAvatar: json['sender_avatar'] ?? json['senderAvatar'],
      messageType: json['message_type'] ?? json['messageType'] ?? 'text',
      content: json['content'] ?? '',
      replyToMessageId: json['reply_to_message_id'] ?? json['replyToMessageId'],
      isRead: json['is_read'] ?? json['isRead'] ?? false,
      createdAt: DateTime.parse(json['created_at'] ?? json['createdAt'] ?? DateTime.now().toIso8601String()),
      readAt: json['read_at'] != null || json['readAt'] != null 
          ? DateTime.parse(json['read_at'] ?? json['readAt']) 
          : null,
    );
  }
}

// 客户信息模型
class CustomerInfo {
  final int id;
  final String firstName;
  final String lastName;
  final String email;
  final String? avatar;
  final String? phone;
  final bool isOnline;

  CustomerInfo({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.email,
    this.avatar,
    this.phone,
    this.isOnline = false,
  });

  String get fullName => '$firstName $lastName';

  factory CustomerInfo.fromJson(Map<String, dynamic> json) {
    return CustomerInfo(
      id: json['id'] ?? 0,
      firstName: json['first_name'] ?? json['firstName'] ?? '',
      lastName: json['last_name'] ?? json['lastName'] ?? '',
      email: json['email'] ?? '',
      avatar: json['avatar'],
      phone: json['phone'],
      isOnline: json['is_online'] ?? json['isOnline'] ?? false,
    );
  }
}

// 管理员信息模型
class AdminInfo {
  final int id;
  final String firstName;
  final String lastName;
  final String email;
  final String? avatar;
  final String role;
  final bool isOnline;

  AdminInfo({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.email,
    this.avatar,
    required this.role,
    this.isOnline = false,
  });

  String get fullName => '$firstName $lastName';

  factory AdminInfo.fromJson(Map<String, dynamic> json) {
    return AdminInfo(
      id: json['id'] ?? 0,
      firstName: json['first_name'] ?? json['firstName'] ?? '',
      lastName: json['last_name'] ?? json['lastName'] ?? '',
      email: json['email'] ?? '',
      avatar: json['avatar'],
      role: json['role'] ?? 'customer_service',
      isOnline: json['is_online'] ?? json['isOnline'] ?? false,
    );
  }
}
