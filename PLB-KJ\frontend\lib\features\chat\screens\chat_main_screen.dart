import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/admin_chat_service.dart';
import '../models/chat_models.dart';
import 'chat_session_screen.dart';
import '../widgets/session_list_item.dart';
import '../widgets/chat_stats_widget.dart';

class ChatMainScreen extends StatefulWidget {
  const ChatMainScreen({Key? key}) : super(key: key);

  @override
  State<ChatMainScreen> createState() => _ChatMainScreenState();
}

class _ChatMainScreenState extends State<ChatMainScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _selectedFilter = 'all';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    
    // 初始化管理端聊天服务
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AdminChatService>().initialize();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('客服聊天管理'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(text: '全部会话', icon: Icon(Icons.chat_bubble_outline)),
            Tab(text: '等待中', icon: Icon(Icons.schedule)),
            Tab(text: '进行中', icon: Icon(Icons.chat)),
            Tab(text: '已结束', icon: Icon(Icons.check_circle_outline)),
          ],
        ),
        actions: [
          Consumer<AdminChatService>(
            builder: (context, chatService, child) {
              return Container(
                margin: const EdgeInsets.only(right: 16),
                child: Row(
                  children: [
                    Icon(
                      chatService.isConnected
                          ? Icons.cloud_done
                          : Icons.cloud_off,
                      color: chatService.isConnected
                          ? Colors.green
                          : Colors.red,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      chatService.isConnected ? '已连接' : '未连接',
                      style: const TextStyle(fontSize: 12),
                    ),
                  ],
                ),
              );
            },
          ),
          IconButton(
            onPressed: () {
              context.read<AdminChatService>().refresh();
            },
            icon: const Icon(Icons.refresh),
            tooltip: '刷新',
          ),
        ],
      ),
      body: Column(
        children: [
          // 统计信息
          Consumer<ChatService>(
            builder: (context, chatService, child) {
              return ChatStatsWidget(sessions: chatService.sessions);
            },
          ),
          
          // 会话列表
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildSessionList('all'),
                _buildSessionList('waiting'),
                _buildSessionList('active'),
                _buildSessionList('closed'),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showCreateSessionDialog,
        backgroundColor: Colors.blue[600],
        child: const Icon(Icons.add_comment, color: Colors.white),
        tooltip: '创建新会话',
      ),
    );
  }

  Widget _buildSessionList(String filter) {
    return Consumer<ChatService>(
      builder: (context, chatService, child) {
        List<ChatSession> filteredSessions = chatService.sessions;
        
        switch (filter) {
          case 'waiting':
            filteredSessions = chatService.sessions
                .where((session) => session.status == 'waiting')
                .toList();
            break;
          case 'active':
            filteredSessions = chatService.sessions
                .where((session) => session.status == 'active')
                .toList();
            break;
          case 'closed':
            filteredSessions = chatService.sessions
                .where((session) => session.status == 'closed')
                .toList();
            break;
        }

        if (filteredSessions.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.chat_bubble_outline,
                  size: 64,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Text(
                  _getEmptyMessage(filter),
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: () async {
            await chatService.refresh();
          },
          child: ListView.builder(
            padding: const EdgeInsets.all(8),
            itemCount: filteredSessions.length,
            itemBuilder: (context, index) {
              final session = filteredSessions[index];
              return SessionListItem(
                session: session,
                onTap: () => _openChatSession(session),
                onAccept: session.status == 'waiting' 
                    ? () => _acceptSession(session)
                    : null,
                onClose: session.status == 'active' 
                    ? () => _closeSession(session)
                    : null,
              );
            },
          ),
        );
      },
    );
  }

  String _getEmptyMessage(String filter) {
    switch (filter) {
      case 'waiting':
        return '暂无等待中的会话';
      case 'active':
        return '暂无进行中的会话';
      case 'closed':
        return '暂无已结束的会话';
      default:
        return '暂无会话';
    }
  }

  void _openChatSession(ChatSession session) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChatSessionScreen(session: session),
      ),
    );
  }

  void _acceptSession(ChatSession session) async {
    try {
      await context.read<ChatService>().acceptSession(session.id);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('会话已接受')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('接受会话失败: $e')),
      );
    }
  }

  void _closeSession(ChatSession session) async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => _buildCloseSessionDialog(),
    );

    if (result != null) {
      try {
        await context.read<ChatService>().closeSession(
          sessionId: session.id,
          rating: result['rating'],
          feedback: result['feedback'],
        );
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('会话已关闭')),
        );
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('关闭会话失败: $e')),
        );
      }
    }
  }

  Widget _buildCloseSessionDialog() {
    int rating = 5;
    String feedback = '';

    return StatefulBuilder(
      builder: (context, setState) {
        return AlertDialog(
          title: const Text('关闭会话'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('请为本次服务评分：'),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(5, (index) {
                  return IconButton(
                    onPressed: () {
                      setState(() {
                        rating = index + 1;
                      });
                    },
                    icon: Icon(
                      index < rating ? Icons.star : Icons.star_border,
                      color: Colors.amber,
                    ),
                  );
                }),
              ),
              const SizedBox(height: 16),
              TextField(
                decoration: const InputDecoration(
                  labelText: '反馈意见（可选）',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
                onChanged: (value) {
                  feedback = value;
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context, {
                  'rating': rating,
                  'feedback': feedback,
                });
              },
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }

  void _showCreateSessionDialog() {
    // 这里可以添加创建新会话的对话框
    // 通常客服端不需要主动创建会话，会话由用户端创建
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('会话由客户端发起创建')),
    );
  }
}
