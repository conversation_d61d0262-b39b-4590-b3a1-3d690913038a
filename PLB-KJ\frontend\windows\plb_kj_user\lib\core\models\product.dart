import 'package:json_annotation/json_annotation.dart';

part 'product.g.dart';

/// 商品模型
@JsonSerializable()
class Product {
  final int id;
  final String name;
  final String? description;
  final String sku;
  final double price;
  @<PERSON>son<PERSON>ey(name: 'compare_price')
  final double? comparePrice;
  @JsonKey(name: 'cost_price')
  final double? costPrice;
  @JsonKey(name: 'stock_quantity')
  final int stockQuantity;
  @JsonKey(name: 'low_stock_threshold')
  final int? lowStockThreshold;
  @JsonKey(name: 'category_id')
  final int? categoryId;
  final String? brand;
  final double? weight;
  final String? dimensions;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_featured')
  final bool isFeatured;
  @JsonKey(name: 'is_digital')
  final bool isDigital;
  @JsonKey(name: 'requires_shipping')
  final bool requiresShipping;
  @<PERSON>son<PERSON>ey(name: 'meta_title')
  final String? metaTitle;
  @JsonKey(name: 'meta_description')
  final String? metaDescription;
  @Json<PERSON>ey(name: 'meta_keywords')
  final String? metaKeywords;
  @JsonKey(name: 'sort_order')
  final int sortOrder;
  @JsonKey(name: 'sales_count')
  final int salesCount;
  @JsonKey(name: 'view_count')
  final int viewCount;
  final ProductStatus status;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  // 关联数据
  final List<ProductImage>? images;
  final List<ProductVariant>? variants;
  final Category? category;

  const Product({
    required this.id,
    required this.name,
    this.description,
    required this.sku,
    required this.price,
    this.comparePrice,
    this.costPrice,
    required this.stockQuantity,
    this.lowStockThreshold,
    this.categoryId,
    this.brand,
    this.weight,
    this.dimensions,
    required this.isFeatured,
    required this.isDigital,
    required this.requiresShipping,
    this.metaTitle,
    this.metaDescription,
    this.metaKeywords,
    required this.sortOrder,
    required this.salesCount,
    required this.viewCount,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    this.images,
    this.variants,
    this.category,
  });

  factory Product.fromJson(Map<String, dynamic> json) =>
      _$ProductFromJson(json);

  Map<String, dynamic> toJson() => _$ProductToJson(this);

  /// 是否有库存
  bool get inStock => stockQuantity > 0;

  /// 是否库存不足
  bool get lowStock => 
      lowStockThreshold != null && stockQuantity <= lowStockThreshold!;

  /// 是否有折扣
  bool get hasDiscount => 
      comparePrice != null && comparePrice! > price;

  /// 折扣百分比
  double get discountPercentage {
    if (!hasDiscount) return 0;
    return ((comparePrice! - price) / comparePrice!) * 100;
  }

  /// 主图片URL
  String? get primaryImageUrl {
    if (images?.isNotEmpty == true) {
      final primaryImage = images!.firstWhere(
        (img) => img.isPrimary,
        orElse: () => images!.first,
      );
      return primaryImage.imageUrl;
    }
    return null;
  }

  /// 状态显示名称
  String get statusDisplayName {
    switch (status) {
      case ProductStatus.active:
        return '上架';
      case ProductStatus.inactive:
        return '下架';
      case ProductStatus.draft:
        return '草稿';
    }
  }
}

/// 商品状态枚举
@JsonEnum()
enum ProductStatus {
  @JsonValue('active')
  active,
  @JsonValue('inactive')
  inactive,
  @JsonValue('draft')
  draft,
}

/// 商品图片模型
@JsonSerializable()
class ProductImage {
  final int id;
  @JsonKey(name: 'product_id')
  final int productId;
  @JsonKey(name: 'image_url')
  final String imageUrl;
  @JsonKey(name: 'alt_text')
  final String? altText;
  @JsonKey(name: 'is_primary')
  final bool isPrimary;
  @JsonKey(name: 'sort_order')
  final int sortOrder;

  const ProductImage({
    required this.id,
    required this.productId,
    required this.imageUrl,
    this.altText,
    required this.isPrimary,
    required this.sortOrder,
  });

  factory ProductImage.fromJson(Map<String, dynamic> json) =>
      _$ProductImageFromJson(json);

  Map<String, dynamic> toJson() => _$ProductImageToJson(this);
}

/// 商品变体模型
@JsonSerializable()
class ProductVariant {
  final int id;
  @JsonKey(name: 'product_id')
  final int productId;
  @JsonKey(name: 'variant_name')
  final String variantName;
  @JsonKey(name: 'variant_value')
  final String variantValue;
  final double? price;
  @JsonKey(name: 'stock_quantity')
  final int? stockQuantity;
  final String? sku;
  @JsonKey(name: 'sort_order')
  final int sortOrder;

  const ProductVariant({
    required this.id,
    required this.productId,
    required this.variantName,
    required this.variantValue,
    this.price,
    this.stockQuantity,
    this.sku,
    required this.sortOrder,
  });

  factory ProductVariant.fromJson(Map<String, dynamic> json) =>
      _$ProductVariantFromJson(json);

  Map<String, dynamic> toJson() => _$ProductVariantToJson(this);

  /// 是否有库存
  bool get inStock => stockQuantity == null || stockQuantity! > 0;
}

/// 商品分类模型
@JsonSerializable()
class Category {
  final int id;
  final String name;
  final String? description;
  @JsonKey(name: 'parent_id')
  final int? parentId;
  @JsonKey(name: 'image_url')
  final String? imageUrl;
  @JsonKey(name: 'sort_order')
  final int sortOrder;
  final bool status;
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  // 关联数据
  final List<Category>? children;
  @JsonKey(name: 'product_count')
  final int? productCount;

  const Category({
    required this.id,
    required this.name,
    this.description,
    this.parentId,
    this.imageUrl,
    required this.sortOrder,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    this.children,
    this.productCount,
  });

  factory Category.fromJson(Map<String, dynamic> json) =>
      _$CategoryFromJson(json);

  Map<String, dynamic> toJson() => _$CategoryToJson(this);

  /// 是否为顶级分类
  bool get isTopLevel => parentId == null || parentId == 0;

  /// 是否有子分类
  bool get hasChildren => children?.isNotEmpty == true;
}
