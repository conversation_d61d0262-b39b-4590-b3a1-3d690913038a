# PLB-KJ 跨境电商聊天服务器

## 功能特性

- 🚀 基于 Socket.IO 的实时聊天
- 👥 支持客户和客服双端通信
- 🔐 JWT 身份验证
- 📱 多种消息类型（文本、图片、文件、语音）
- 💾 MySQL 数据持久化
- 🔄 Redis 缓存支持
- 📊 在线状态管理
- 📈 聊天统计分析
- 🎯 会话自动分配
- ⭐ 客户满意度评价

## 技术栈

- **后端**: Node.js + Express + Socket.IO
- **数据库**: MySQL 8.0+
- **缓存**: Redis 6.0+
- **认证**: JWT
- **前端**: Flutter (管理端)

## 快速开始

### 1. 环境要求

- Node.js 16.0+
- MySQL 8.0+
- Redis 6.0+
- npm 或 yarn

### 2. 安装依赖

```bash
cd PLB-KJ/backend/chat-server
npm install
```

### 3. 数据库配置

1. 创建数据库：
```sql
CREATE DATABASE plb_kj_ecommerce CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 导入聊天表结构：
```bash
mysql -u root -p plb_kj_ecommerce < ../chat_database_schema.sql
```

### 4. 环境配置

复制并编辑环境配置文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置数据库和Redis连接信息：
```env
# 聊天服务器配置
PORT=3000
NODE_ENV=development

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=plb_kj_ecommerce
DB_USER=root
DB_PASSWORD=your_password

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT配置
JWT_SECRET=plb-kj-chat-server-secret-key-2024
JWT_EXPIRES_IN=24h
```

### 5. 启动服务器

#### 开发模式
```bash
npm run dev
```

#### 生产模式
```bash
npm start
```

#### Windows 批处理启动
```bash
start-chat-server.bat
```

### 6. 验证安装

访问健康检查端点：
```
GET http://localhost:3000/health
```

预期响应：
```json
{
  "status": "ok",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "onlineUsers": 0
}
```

## API 文档

### WebSocket 事件

#### 客户端发送事件

| 事件名 | 描述 | 参数 |
|--------|------|------|
| `send_message` | 发送消息 | `{sessionId, content, messageType, replyToMessageId}` |
| `create_session` | 创建会话 | `{title, category, priority}` |
| `accept_session` | 接受会话 | `{sessionId}` |
| `close_session` | 关闭会话 | `{sessionId, rating, feedback}` |
| `mark_messages_read` | 标记已读 | `{sessionId, messageIds}` |
| `typing` | 输入状态 | `{sessionId, isTyping}` |

#### 服务器发送事件

| 事件名 | 描述 | 数据格式 |
|--------|------|----------|
| `connected` | 连接成功 | `{user, activeSessions}` |
| `new_message` | 新消息 | `{id, sessionId, senderType, content, ...}` |
| `session_created` | 会话创建 | `{id, sessionId, customerId, title, ...}` |
| `session_accepted` | 会话接受 | `{sessionId, agent}` |
| `session_closed` | 会话关闭 | `{sessionId, closedBy, rating}` |
| `user_typing` | 用户输入 | `{userId, userType, isTyping}` |
| `messages_read` | 消息已读 | `{sessionId, messageIds, readBy}` |

### REST API

#### 获取会话消息
```
GET /api/chat/sessions/:sessionId/messages
Authorization: Bearer <token>
```

#### 上传文件
```
POST /api/chat/upload
Authorization: Bearer <token>
Content-Type: multipart/form-data
```

## 数据库结构

### 主要表

- `plb_kj_chat_sessions` - 聊天会话
- `plb_kj_chat_messages` - 聊天消息
- `plb_kj_online_status` - 在线状态
- `plb_kj_chat_files` - 聊天文件
- `plb_kj_chat_stats` - 聊天统计

### 视图

- `plb_kj_chat_session_details` - 会话详情视图

### 存储过程

- `UpdateChatStats` - 更新聊天统计

## 部署指南

### Docker 部署

1. 构建镜像：
```bash
docker build -t plb-kj-chat-server .
```

2. 运行容器：
```bash
docker run -d \
  --name plb-kj-chat \
  -p 3000:3000 \
  -e DB_HOST=your_db_host \
  -e DB_PASSWORD=your_db_password \
  -e REDIS_HOST=your_redis_host \
  plb-kj-chat-server
```

### PM2 部署

1. 安装 PM2：
```bash
npm install -g pm2
```

2. 启动应用：
```bash
pm2 start server.js --name plb-kj-chat
```

3. 保存配置：
```bash
pm2 save
pm2 startup
```

### Nginx 反向代理

```nginx
upstream chat_backend {
    server 127.0.0.1:3000;
}

server {
    listen 80;
    server_name chat.yourdomain.com;

    location / {
        proxy_pass http://chat_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 监控和日志

### 日志配置

日志文件位置：`./logs/`
- `app.log` - 应用日志
- `error.log` - 错误日志
- `access.log` - 访问日志

### 性能监控

推荐使用以下工具：
- **应用监控**: PM2 Monitor, New Relic
- **数据库监控**: MySQL Workbench, Percona Monitoring
- **Redis监控**: Redis Commander, RedisInsight

## 故障排除

### 常见问题

1. **连接失败**
   - 检查数据库连接配置
   - 确认 Redis 服务运行状态
   - 验证防火墙设置

2. **消息发送失败**
   - 检查 JWT token 有效性
   - 确认用户权限
   - 查看服务器日志

3. **文件上传失败**
   - 检查文件大小限制
   - 确认上传目录权限
   - 验证文件类型限制

### 调试模式

启用调试日志：
```bash
DEBUG=socket.io:* npm start
```

## 安全建议

1. **JWT 密钥**: 使用强随机密钥
2. **HTTPS**: 生产环境启用 SSL/TLS
3. **CORS**: 配置正确的跨域策略
4. **文件上传**: 限制文件类型和大小
5. **速率限制**: 实施 API 调用限制
6. **输入验证**: 验证所有用户输入

## 许可证

MIT License

## 支持

如有问题，请联系开发团队或提交 Issue。
