-- ========================================
-- PLB-KJ 跨境电商管理系统 - 完整数据库脚本
-- 整合版本 - MySQL 5.7+ 兼容
-- 包含：用户管理、聊天系统、商品管理、订单系统
-- ========================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;
SET sql_mode = 'ONLY_FULL_GROUP_BY,STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION';

-- ========================================
-- 1. 用户管理模块
-- ========================================

-- 1.1 管理员用户表
CREATE TABLE IF NOT EXISTS `plb_kj_admin_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `first_name` varchar(50) DEFAULT NULL,
  `last_name` varchar(50) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `avatar` varchar(255) DEFAULT NULL,
  `role` enum('admin','manager','staff','customer_service') NOT NULL DEFAULT 'staff',
  `department` varchar(50) DEFAULT NULL,
  `is_online` tinyint(1) NOT NULL DEFAULT '0',
  `last_login_at` timestamp NULL DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  KEY `idx_role` (`role`),
  KEY `idx_status` (`status`),
  KEY `idx_is_online` (`is_online`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员用户表';

-- 1.2 客户表
CREATE TABLE IF NOT EXISTS `plb_kj_customers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) DEFAULT NULL,
  `email` varchar(100) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `first_name` varchar(50) NOT NULL,
  `last_name` varchar(50) NOT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `country` varchar(50) DEFAULT NULL,
  `city` varchar(50) DEFAULT NULL,
  `address` text,
  `postal_code` varchar(20) DEFAULT NULL,
  `avatar` varchar(255) DEFAULT NULL,
  `language_preference` varchar(10) NOT NULL DEFAULT 'zh-CN',
  `currency_preference` varchar(3) NOT NULL DEFAULT 'USD',
  `is_online` tinyint(1) NOT NULL DEFAULT '0',
  `last_login_at` timestamp NULL DEFAULT NULL,
  `email_verified` tinyint(1) NOT NULL DEFAULT '0',
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  KEY `idx_status` (`status`),
  KEY `idx_is_online` (`is_online`),
  KEY `idx_email_verified` (`email_verified`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户表';

-- ========================================
-- 2. 聊天系统模块
-- ========================================

-- 2.1 聊天会话表
CREATE TABLE IF NOT EXISTS `plb_kj_chat_sessions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` varchar(36) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `admin_user_id` int(11) DEFAULT NULL,
  `title` varchar(255) NOT NULL DEFAULT '客服咨询',
  `status` enum('waiting','active','closed') NOT NULL DEFAULT 'waiting',
  `priority` enum('low','normal','high','urgent') NOT NULL DEFAULT 'normal',
  `category` enum('general','technical','billing','complaint') NOT NULL DEFAULT 'general',
  `started_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `ended_at` timestamp NULL DEFAULT NULL,
  `last_message_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `customer_satisfaction_rating` tinyint(1) DEFAULT NULL,
  `customer_feedback` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `session_id` (`session_id`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_admin_user_id` (`admin_user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_last_message_at` (`last_message_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天会话表';

-- 2.2 聊天消息表
CREATE TABLE IF NOT EXISTS `plb_kj_chat_messages` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `session_id` int(11) NOT NULL,
  `sender_type` enum('customer','admin','system') NOT NULL,
  `sender_id` int(11) NOT NULL,
  `message_type` enum('text','image','file','system','quick_reply') NOT NULL DEFAULT 'text',
  `content` text NOT NULL,
  `sender_name` varchar(100) NOT NULL,
  `sender_avatar` varchar(255) DEFAULT NULL,
  `is_read` tinyint(1) NOT NULL DEFAULT '0',
  `read_at` timestamp NULL DEFAULT NULL,
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0',
  `reply_to_message_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_sender` (`sender_type`,`sender_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_is_read` (`is_read`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天消息表';

-- 2.3 快捷回复模板表
CREATE TABLE IF NOT EXISTS `plb_kj_chat_quick_replies` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category` varchar(50) NOT NULL,
  `title` varchar(100) NOT NULL,
  `content` text NOT NULL,
  `sort_order` int(11) NOT NULL DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_by` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_category` (`category`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='快捷回复模板表';

-- 2.4 在线状态表
CREATE TABLE IF NOT EXISTS `plb_kj_online_status` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_type` enum('customer','admin') NOT NULL,
  `user_id` int(11) NOT NULL,
  `socket_id` varchar(100) DEFAULT NULL,
  `last_seen` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user` (`user_type`,`user_id`),
  KEY `idx_last_seen` (`last_seen`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='在线状态表';

-- 2.5 聊天文件表
CREATE TABLE IF NOT EXISTS `plb_kj_chat_files` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `message_id` int(11) NOT NULL,
  `original_name` varchar(255) NOT NULL,
  `file_name` varchar(255) NOT NULL,
  `file_path` varchar(500) NOT NULL,
  `file_size` bigint(20) NOT NULL,
  `mime_type` varchar(100) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_message_id` (`message_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='聊天文件表';

-- ========================================
-- 3. 商品管理模块
-- ========================================

-- 3.1 商品分类表
CREATE TABLE IF NOT EXISTS `plb_kj_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `name_en` varchar(100) DEFAULT NULL,
  `description` text,
  `description_en` text,
  `parent_id` int(11) NOT NULL DEFAULT '0',
  `level` tinyint(1) NOT NULL DEFAULT '1',
  `path` varchar(500) DEFAULT NULL,
  `sort_order` int(11) NOT NULL DEFAULT '0',
  `image` varchar(255) DEFAULT NULL,
  `seo_title` varchar(200) DEFAULT NULL,
  `seo_description` text,
  `status` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品分类表';

-- 3.2 商品表
CREATE TABLE IF NOT EXISTS `plb_kj_products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(200) NOT NULL,
  `name_en` varchar(200) DEFAULT NULL,
  `description` text,
  `description_en` text,
  `category_id` int(11) NOT NULL,
  `sku` varchar(100) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `compare_price` decimal(10,2) DEFAULT NULL,
  `cost_price` decimal(10,2) DEFAULT NULL,
  `weight` decimal(8,2) DEFAULT NULL,
  `weight_unit` varchar(10) DEFAULT 'kg',
  `dimensions` varchar(100) DEFAULT NULL,
  `inventory_quantity` int(11) NOT NULL DEFAULT '0',
  `inventory_policy` enum('deny','continue') NOT NULL DEFAULT 'deny',
  `track_inventory` tinyint(1) NOT NULL DEFAULT '1',
  `requires_shipping` tinyint(1) NOT NULL DEFAULT '1',
  `taxable` tinyint(1) NOT NULL DEFAULT '1',
  `tags` varchar(500) DEFAULT NULL,
  `vendor` varchar(100) DEFAULT NULL,
  `product_type` varchar(100) DEFAULT NULL,
  `status` enum('active','draft','archived') NOT NULL DEFAULT 'draft',
  `is_featured` tinyint(1) NOT NULL DEFAULT '0',
  `meta_title` varchar(200) DEFAULT NULL,
  `meta_description` text,
  `handle` varchar(200) DEFAULT NULL,
  `published_at` timestamp NULL DEFAULT NULL,
  `sales_count` int(11) NOT NULL DEFAULT '0',
  `rating_average` decimal(3,2) NOT NULL DEFAULT '0.00',
  `rating_count` int(11) NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `sku` (`sku`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_status` (`status`),
  KEY `idx_is_featured` (`is_featured`),
  KEY `idx_price` (`price`),
  FULLTEXT KEY `idx_search` (`name`,`description`,`tags`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品表';

-- 3.3 商品图片表
CREATE TABLE IF NOT EXISTS `plb_kj_product_images` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `image_url` varchar(255) NOT NULL,
  `thumbnail_url` varchar(255) DEFAULT NULL,
  `alt_text` varchar(200) DEFAULT NULL,
  `sort_order` int(11) NOT NULL DEFAULT '0',
  `is_primary` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品图片表';

-- 3.4 商品变体表
CREATE TABLE IF NOT EXISTS `plb_kj_product_variants` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL,
  `sku` varchar(100) NOT NULL,
  `title` varchar(200) DEFAULT NULL,
  `price` decimal(10,2) DEFAULT NULL,
  `compare_price` decimal(10,2) DEFAULT NULL,
  `cost_price` decimal(10,2) DEFAULT NULL,
  `inventory_quantity` int(11) NOT NULL DEFAULT '0',
  `inventory_policy` enum('deny','continue') NOT NULL DEFAULT 'deny',
  `weight` decimal(8,2) DEFAULT NULL,
  `weight_unit` varchar(10) DEFAULT 'kg',
  `requires_shipping` tinyint(1) NOT NULL DEFAULT '1',
  `taxable` tinyint(1) NOT NULL DEFAULT '1',
  `barcode` varchar(100) DEFAULT NULL,
  `option1_name` varchar(50) DEFAULT NULL,
  `option1_value` varchar(100) DEFAULT NULL,
  `option2_name` varchar(50) DEFAULT NULL,
  `option2_value` varchar(100) DEFAULT NULL,
  `option3_name` varchar(50) DEFAULT NULL,
  `option3_value` varchar(100) DEFAULT NULL,
  `image_url` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `sku` (`sku`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品变体表';

-- ========================================
-- 4. 订单管理模块
-- ========================================

-- 4.1 货币表
CREATE TABLE IF NOT EXISTS `plb_kj_currencies` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(3) NOT NULL,
  `name` varchar(50) NOT NULL,
  `symbol` varchar(10) NOT NULL,
  `rate` decimal(10,6) NOT NULL,
  `is_default` tinyint(1) NOT NULL DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`),
  KEY `idx_is_default` (`is_default`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='货币表';

-- 4.2 订单表
CREATE TABLE IF NOT EXISTS `plb_kj_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_number` varchar(50) NOT NULL,
  `customer_id` int(11) NOT NULL,
  `currency_id` int(11) NOT NULL,
  `subtotal` decimal(10,2) NOT NULL,
  `tax_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `shipping_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `discount_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `total_amount` decimal(10,2) NOT NULL,
  `status` enum('pending','confirmed','processing','shipped','delivered','cancelled','refunded') NOT NULL DEFAULT 'pending',
  `payment_status` enum('pending','paid','failed','refunded','partially_refunded') NOT NULL DEFAULT 'pending',
  `payment_method` varchar(50) DEFAULT NULL,
  `payment_reference` varchar(100) DEFAULT NULL,
  `shipping_method` varchar(50) DEFAULT NULL,
  `tracking_number` varchar(100) DEFAULT NULL,
  `notes` text,
  `admin_note` text,
  `tags` varchar(500) DEFAULT NULL,
  `source` varchar(50) NOT NULL DEFAULT 'web',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_number` (`order_number`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_status` (`status`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单表';

-- 4.3 订单商品表
CREATE TABLE IF NOT EXISTS `plb_kj_order_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `variant_id` int(11) DEFAULT NULL,
  `quantity` int(11) NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `total_price` decimal(10,2) NOT NULL,
  `product_name` varchar(200) NOT NULL,
  `variant_title` varchar(200) DEFAULT NULL,
  `sku` varchar(100) DEFAULT NULL,
  `weight` decimal(8,2) DEFAULT NULL,
  `requires_shipping` tinyint(1) NOT NULL DEFAULT '1',
  `fulfillment_status` enum('unfulfilled','fulfilled','cancelled') NOT NULL DEFAULT 'unfulfilled',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单商品表';

-- 4.4 购物车表
CREATE TABLE IF NOT EXISTS `plb_kj_shopping_cart` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `customer_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `variant_id` int(11) DEFAULT NULL,
  `quantity` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_customer_product_variant` (`customer_id`,`product_id`,`variant_id`),
  KEY `idx_customer_id` (`customer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='购物车表';

-- 4.5 客户收藏表
CREATE TABLE IF NOT EXISTS `plb_kj_customer_favorites` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `customer_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_customer_product` (`customer_id`,`product_id`),
  KEY `idx_customer_id` (`customer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户收藏表';

-- 4.6 客户地址表
CREATE TABLE IF NOT EXISTS `plb_kj_customer_addresses` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `customer_id` int(11) NOT NULL,
  `type` enum('shipping','billing','both') NOT NULL DEFAULT 'shipping',
  `first_name` varchar(50) NOT NULL,
  `last_name` varchar(50) NOT NULL,
  `company` varchar(100) DEFAULT NULL,
  `address1` varchar(255) NOT NULL,
  `address2` varchar(255) DEFAULT NULL,
  `city` varchar(100) NOT NULL,
  `province` varchar(100) DEFAULT NULL,
  `country` varchar(100) NOT NULL,
  `postal_code` varchar(20) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `is_default` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_customer_id` (`customer_id`),
  KEY `idx_is_default` (`is_default`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='客户地址表';

-- ========================================
-- 5. 系统管理模块
-- ========================================

-- 5.1 系统设置表
CREATE TABLE IF NOT EXISTS `plb_kj_system_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text,
  `setting_type` enum('string','number','boolean','json') NOT NULL DEFAULT 'string',
  `category` varchar(50) NOT NULL DEFAULT 'general',
  `description` varchar(255) DEFAULT NULL,
  `is_public` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`),
  KEY `idx_category` (`category`),
  KEY `idx_is_public` (`is_public`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统设置表';

-- 5.2 通知表
CREATE TABLE IF NOT EXISTS `plb_kj_notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `recipient_type` enum('customer','admin') NOT NULL,
  `recipient_id` int(11) NOT NULL,
  `type` varchar(50) NOT NULL,
  `title` varchar(200) NOT NULL,
  `content` text,
  `data` text COMMENT 'JSON格式数据',
  `is_read` tinyint(1) NOT NULL DEFAULT '0',
  `read_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_recipient` (`recipient_type`,`recipient_id`),
  KEY `idx_is_read` (`is_read`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='通知表';

-- 5.3 操作日志表
CREATE TABLE IF NOT EXISTS `plb_kj_activity_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_type` enum('customer','admin') NOT NULL,
  `user_id` int(11) NOT NULL,
  `action` varchar(100) NOT NULL,
  `description` text,
  `model_type` varchar(100) DEFAULT NULL,
  `model_id` int(11) DEFAULT NULL,
  `properties` text COMMENT 'JSON格式的属性数据',
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user` (`user_type`,`user_id`),
  KEY `idx_action` (`action`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';

-- 5.4 文件上传表
CREATE TABLE IF NOT EXISTS `plb_kj_file_uploads` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `original_name` varchar(255) NOT NULL,
  `file_name` varchar(255) NOT NULL,
  `file_path` varchar(500) NOT NULL,
  `file_size` bigint(20) NOT NULL,
  `mime_type` varchar(100) NOT NULL,
  `file_type` enum('image','document','video','audio','other') NOT NULL DEFAULT 'other',
  `uploaded_by_type` enum('customer','admin') NOT NULL,
  `uploaded_by_id` int(11) NOT NULL,
  `is_public` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_uploaded_by` (`uploaded_by_type`,`uploaded_by_id`),
  KEY `idx_file_type` (`file_type`),
  KEY `idx_is_public` (`is_public`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件上传表';

-- ========================================
-- 6. 外键约束
-- ========================================

-- 聊天系统外键
ALTER TABLE `plb_kj_chat_sessions`
ADD CONSTRAINT `fk_chat_sessions_customer` FOREIGN KEY (`customer_id`) REFERENCES `plb_kj_customers` (`id`) ON DELETE CASCADE,
ADD CONSTRAINT `fk_chat_sessions_admin` FOREIGN KEY (`admin_user_id`) REFERENCES `plb_kj_admin_users` (`id`) ON DELETE SET NULL;

ALTER TABLE `plb_kj_chat_messages`
ADD CONSTRAINT `fk_chat_messages_session` FOREIGN KEY (`session_id`) REFERENCES `plb_kj_chat_sessions` (`id`) ON DELETE CASCADE,
ADD CONSTRAINT `fk_chat_messages_reply` FOREIGN KEY (`reply_to_message_id`) REFERENCES `plb_kj_chat_messages` (`id`) ON DELETE SET NULL;

ALTER TABLE `plb_kj_chat_quick_replies`
ADD CONSTRAINT `fk_quick_replies_creator` FOREIGN KEY (`created_by`) REFERENCES `plb_kj_admin_users` (`id`) ON DELETE CASCADE;

ALTER TABLE `plb_kj_chat_files`
ADD CONSTRAINT `fk_chat_files_message` FOREIGN KEY (`message_id`) REFERENCES `plb_kj_chat_messages` (`id`) ON DELETE CASCADE;

-- 商品系统外键
ALTER TABLE `plb_kj_products`
ADD CONSTRAINT `fk_products_category` FOREIGN KEY (`category_id`) REFERENCES `plb_kj_categories` (`id`) ON DELETE RESTRICT;

ALTER TABLE `plb_kj_product_images`
ADD CONSTRAINT `fk_product_images_product` FOREIGN KEY (`product_id`) REFERENCES `plb_kj_products` (`id`) ON DELETE CASCADE;

ALTER TABLE `plb_kj_product_variants`
ADD CONSTRAINT `fk_product_variants_product` FOREIGN KEY (`product_id`) REFERENCES `plb_kj_products` (`id`) ON DELETE CASCADE;

-- 订单系统外键
ALTER TABLE `plb_kj_orders`
ADD CONSTRAINT `fk_orders_customer` FOREIGN KEY (`customer_id`) REFERENCES `plb_kj_customers` (`id`) ON DELETE RESTRICT,
ADD CONSTRAINT `fk_orders_currency` FOREIGN KEY (`currency_id`) REFERENCES `plb_kj_currencies` (`id`) ON DELETE RESTRICT;

ALTER TABLE `plb_kj_order_items`
ADD CONSTRAINT `fk_order_items_order` FOREIGN KEY (`order_id`) REFERENCES `plb_kj_orders` (`id`) ON DELETE CASCADE,
ADD CONSTRAINT `fk_order_items_product` FOREIGN KEY (`product_id`) REFERENCES `plb_kj_products` (`id`) ON DELETE RESTRICT,
ADD CONSTRAINT `fk_order_items_variant` FOREIGN KEY (`variant_id`) REFERENCES `plb_kj_product_variants` (`id`) ON DELETE SET NULL;

ALTER TABLE `plb_kj_shopping_cart`
ADD CONSTRAINT `fk_shopping_cart_customer` FOREIGN KEY (`customer_id`) REFERENCES `plb_kj_customers` (`id`) ON DELETE CASCADE,
ADD CONSTRAINT `fk_shopping_cart_product` FOREIGN KEY (`product_id`) REFERENCES `plb_kj_products` (`id`) ON DELETE CASCADE,
ADD CONSTRAINT `fk_shopping_cart_variant` FOREIGN KEY (`variant_id`) REFERENCES `plb_kj_product_variants` (`id`) ON DELETE CASCADE;

ALTER TABLE `plb_kj_customer_favorites`
ADD CONSTRAINT `fk_customer_favorites_customer` FOREIGN KEY (`customer_id`) REFERENCES `plb_kj_customers` (`id`) ON DELETE CASCADE,
ADD CONSTRAINT `fk_customer_favorites_product` FOREIGN KEY (`product_id`) REFERENCES `plb_kj_products` (`id`) ON DELETE CASCADE;

ALTER TABLE `plb_kj_customer_addresses`
ADD CONSTRAINT `fk_customer_addresses_customer` FOREIGN KEY (`customer_id`) REFERENCES `plb_kj_customers` (`id`) ON DELETE CASCADE;

-- ========================================
-- 7. 示例数据插入
-- ========================================

-- 7.1 插入默认管理员用户
INSERT IGNORE INTO `plb_kj_admin_users` (`username`, `email`, `password_hash`, `first_name`, `last_name`, `role`, `status`) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '系统', '管理员', 'admin', 1),
('manager', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '业务', '经理', 'manager', 1),
('service1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '客服', '小王', 'customer_service', 1),
('service2', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '客服', '小李', 'customer_service', 1);

-- 7.2 插入示例客户数据
INSERT IGNORE INTO `plb_kj_customers` (`username`, `email`, `password_hash`, `first_name`, `last_name`, `phone`, `country`, `status`) VALUES
('customer1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '张', '三', '+86-13800138001', 'China', 1),
('customer2', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '李', '四', '+86-13800138002', 'China', 1),
('customer3', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'John', 'Smith', '******-0123', 'USA', 1),
('customer4', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Emma', 'Johnson', '+44-20-7946-0958', 'UK', 1);

-- 7.3 插入默认货币
INSERT IGNORE INTO `plb_kj_currencies` (`code`, `name`, `symbol`, `rate`, `is_default`, `is_active`) VALUES
('USD', 'US Dollar', '$', 1.000000, 1, 1),
('EUR', 'Euro', '€', 0.850000, 0, 1),
('GBP', 'British Pound', '£', 0.750000, 0, 1),
('CNY', 'Chinese Yuan', '¥', 7.200000, 0, 1),
('JPY', 'Japanese Yen', '¥', 110.000000, 0, 1);

-- 7.4 插入默认商品分类
INSERT IGNORE INTO `plb_kj_categories` (`name`, `name_en`, `description`, `description_en`, `parent_id`, `level`, `path`, `sort_order`) VALUES
('电子产品', 'Electronics', '各类电子产品', 'Various electronic products', 0, 1, '1', 1),
('服装配饰', 'Fashion', '时尚服装和配饰', 'Fashion clothing and accessories', 0, 1, '2', 2),
('家居用品', 'Home & Garden', '家居装饰和园艺用品', 'Home decoration and garden supplies', 0, 1, '3', 3),
('运动户外', 'Sports & Outdoors', '运动和户外用品', 'Sports and outdoor equipment', 0, 1, '4', 4),
('美妆护肤', 'Beauty & Personal Care', '美容和个人护理产品', 'Beauty and personal care products', 0, 1, '5', 5);

-- 插入子分类
INSERT IGNORE INTO `plb_kj_categories` (`name`, `name_en`, `description`, `description_en`, `parent_id`, `level`, `path`, `sort_order`) VALUES
('手机数码', 'Mobile & Digital', '手机和数码产品', 'Mobile phones and digital products', 1, 2, '1/6', 1),
('电脑办公', 'Computers & Office', '电脑和办公设备', 'Computers and office equipment', 1, 2, '1/7', 2),
('男装', 'Men\'s Clothing', '男士服装', 'Men\'s clothing', 2, 2, '2/8', 1),
('女装', 'Women\'s Clothing', '女士服装', 'Women\'s clothing', 2, 2, '2/9', 2),
('家具', 'Furniture', '各类家具', 'Various furniture', 3, 2, '3/10', 1);

-- 7.5 插入快捷回复模板
INSERT IGNORE INTO `plb_kj_chat_quick_replies` (`category`, `title`, `content`, `sort_order`, `created_by`) VALUES
('greeting', '欢迎语', '您好！欢迎来到PLB-KJ跨境电商平台，我是您的专属客服，有什么可以帮助您的吗？', 1, 1),
('common', '稍等回复', '好的，请稍等，我马上为您查询处理。', 2, 1),
('common', '感谢回复', '感谢您的耐心等待，如果还有其他问题，请随时联系我们。', 3, 1),
('technical', '技术支持', '我来为您解决技术问题，请详细描述您遇到的情况，最好能提供截图。', 4, 1),
('billing', '账单查询', '我来帮您查看账单详情，请提供您的订单号或账户信息。', 5, 1),
('shipping', '物流查询', '我来帮您查询物流信息，请提供您的订单号或快递单号。', 6, 1),
('return', '退换货政策', '我们支持7天无理由退换货，商品需保持原包装和标签完整。具体退换货流程请查看我们的退换货政策页面。', 7, 1),
('payment', '支付问题', '我们支持多种支付方式：信用卡、PayPal、银行转账、支付宝等。如果支付遇到问题，请截图发给我。', 8, 1),
('product', '产品咨询', '关于产品的详细信息，我来为您详细介绍。请告诉我您感兴趣的具体产品。', 9, 1),
('closing', '结束语', '感谢您选择PLB-KJ跨境电商平台，如果还有其他问题，随时联系我们。祝您购物愉快！', 10, 1);

-- 7.6 插入系统设置
INSERT IGNORE INTO `plb_kj_system_settings` (`setting_key`, `setting_value`, `setting_type`, `category`, `description`, `is_public`) VALUES
-- 基本设置
('site_name', 'PLB-KJ跨境电商管理系统', 'string', 'general', '网站名称', 1),
('site_email', '<EMAIL>', 'string', 'general', '网站邮箱', 0),
('site_url', 'http://localhost/PLB-KJ', 'string', 'general', '网站URL', 1),
('currency_default', 'USD', 'string', 'general', '默认货币', 1),
('tax_rate', '0.00', 'number', 'general', '默认税率', 1),
('timezone', 'Asia/Shanghai', 'string', 'general', '时区设置', 1),
('language_default', 'zh-CN', 'string', 'general', '默认语言', 1),

-- 聊天系统设置
('chat_enabled', '1', 'boolean', 'chat', '是否启用聊天功能', 1),
('chat_max_sessions_per_agent', '5', 'number', 'chat', '每个客服最大并发会话数', 0),
('chat_auto_assign', '1', 'boolean', 'chat', '是否自动分配客服', 0),
('chat_session_timeout', '1800', 'number', 'chat', '会话超时时间(秒)', 0),
('chat_file_upload_enabled', '1', 'boolean', 'chat', '是否允许文件上传', 0),
('chat_file_max_size', '10485760', 'number', 'chat', '文件上传最大大小(字节)', 0),
('chat_allowed_file_types', 'jpg,jpeg,png,gif,pdf,doc,docx,txt', 'string', 'chat', '允许的文件类型', 0),

-- 邮件设置
('mail_driver', 'smtp', 'string', 'mail', '邮件驱动', 0),
('mail_host', 'smtp.gmail.com', 'string', 'mail', 'SMTP主机', 0),
('mail_port', '587', 'number', 'mail', 'SMTP端口', 0),
('mail_encryption', 'tls', 'string', 'mail', '加密方式', 0),

-- 安全设置
('security_password_min_length', '8', 'number', 'security', '密码最小长度', 0),
('security_login_attempts', '5', 'number', 'security', '登录尝试次数限制', 0),
('security_session_lifetime', '7200', 'number', 'security', '会话生命周期(秒)', 0),

-- API设置
('api_rate_limit', '1000', 'number', 'api', 'API请求频率限制(每小时)', 0),
('api_timeout', '30', 'number', 'api', 'API请求超时时间(秒)', 0);

-- 7.7 插入示例聊天会话
INSERT IGNORE INTO `plb_kj_chat_sessions` (`session_id`, `customer_id`, `admin_user_id`, `title`, `status`, `priority`, `category`, `started_at`, `last_message_at`) VALUES
('550e8400-e29b-41d4-a716-446655440001', 1, 3, '产品咨询 - iPhone 15', 'active', 'normal', 'general', NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440002', 2, NULL, '技术支持 - 支付问题', 'waiting', 'high', 'technical', NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440003', 3, 3, '账单问题', 'closed', 'normal', 'billing', DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY)),
('550e8400-e29b-41d4-a716-446655440004', 4, 4, '退换货咨询', 'active', 'normal', 'general', DATE_SUB(NOW(), INTERVAL 2 HOUR), DATE_SUB(NOW(), INTERVAL 30 MINUTE)),
('550e8400-e29b-41d4-a716-446655440005', 1, NULL, '物流查询', 'waiting', 'low', 'general', DATE_SUB(NOW(), INTERVAL 1 HOUR), DATE_SUB(NOW(), INTERVAL 1 HOUR));

-- 7.8 插入示例聊天消息
INSERT IGNORE INTO `plb_kj_chat_messages` (`session_id`, `sender_type`, `sender_id`, `sender_name`, `message_type`, `content`, `is_read`) VALUES
-- 会话1的消息
(1, 'customer', 1, '张三', 'text', '你好，我想咨询一下iPhone 15的价格和配置', 1),
(1, 'admin', 3, '客服小王', 'text', '您好！很高兴为您服务。iPhone 15目前有多个版本，请问您想了解哪个版本呢？', 1),
(1, 'customer', 1, '张三', 'text', '我想要128GB的标准版', 1),
(1, 'admin', 3, '客服小王', 'text', 'iPhone 15 128GB标准版目前售价$799，支持全球发货。这款手机采用A16仿生芯片，6.1英寸超视网膜XDR显示屏。您还想了解其他信息吗？', 0),

-- 会话2的消息
(2, 'customer', 2, '李四', 'text', '我在支付时遇到问题，信用卡一直提示失败', 0),

-- 会话3的消息（已结束）
(3, 'customer', 3, 'John Smith', 'text', '我的账单金额不对，显示多收了运费', 1),
(3, 'admin', 3, '客服小王', 'text', '我来帮您查看一下账单详情，请稍等。', 1),
(3, 'admin', 3, '客服小王', 'text', '经过核实，确实是系统计算错误，多收了$15运费。我已经为您申请退款，3-5个工作日内会退回到您的账户。', 1),
(3, 'customer', 3, 'John Smith', 'text', '好的，谢谢您的帮助！', 1),

-- 会话4的消息
(4, 'customer', 4, 'Emma Johnson', 'text', '我想退换一件衣服，但是标签已经剪掉了', 1),
(4, 'admin', 4, '客服小李', 'text', '您好！请问是什么原因需要退换呢？虽然标签剪掉了，但如果是质量问题，我们依然可以为您处理。', 0);

-- 7.9 插入示例商品数据
INSERT IGNORE INTO `plb_kj_products` (`name`, `name_en`, `description`, `category_id`, `sku`, `price`, `compare_price`, `inventory_quantity`, `status`, `is_featured`) VALUES
('iPhone 15 128GB', 'iPhone 15 128GB', '苹果最新款智能手机，搭载A16仿生芯片', 6, 'IPHONE15-128GB', 799.00, 899.00, 50, 'active', 1),
('MacBook Air M2', 'MacBook Air M2', '轻薄便携的笔记本电脑，搭载M2芯片', 7, 'MBA-M2-256GB', 1199.00, 1299.00, 30, 'active', 1),
('Nike Air Max 270', 'Nike Air Max 270', '舒适的运动鞋，适合日常穿着', 4, 'NIKE-AM270-BLK', 150.00, 180.00, 100, 'active', 0),
('Levi\'s 501 牛仔裤', 'Levi\'s 501 Jeans', '经典款牛仔裤，多种尺码可选', 8, 'LEVIS-501-BLUE', 89.00, 120.00, 200, 'active', 0);

-- ========================================
-- 8. 视图创建
-- ========================================

-- 8.1 聊天会话详情视图
CREATE OR REPLACE VIEW `plb_kj_chat_session_details` AS
SELECT
    s.id,
    s.session_id,
    s.customer_id,
    s.admin_user_id,
    s.title,
    s.status,
    s.priority,
    s.category,
    s.started_at,
    s.ended_at,
    s.last_message_at,
    s.customer_satisfaction_rating,
    s.customer_feedback,
    COALESCE(c.first_name, '') as customer_first_name,
    COALESCE(c.last_name, '') as customer_last_name,
    COALESCE(c.email, '') as customer_email,
    COALESCE(c.avatar, '') as customer_avatar,
    COALESCE(c.is_online, 0) as customer_is_online,
    COALESCE(a.first_name, '') as admin_first_name,
    COALESCE(a.last_name, '') as admin_last_name,
    COALESCE(a.email, '') as admin_email,
    COALESCE(a.avatar, '') as admin_avatar,
    COALESCE(a.is_online, 0) as admin_is_online,
    (SELECT COUNT(*) FROM plb_kj_chat_messages m WHERE m.session_id = s.id AND m.is_read = 0 AND m.sender_type = 'customer') as unread_count
FROM plb_kj_chat_sessions s
LEFT JOIN plb_kj_customers c ON s.customer_id = c.id
LEFT JOIN plb_kj_admin_users a ON s.admin_user_id = a.id;

-- 8.2 产品详情视图
CREATE OR REPLACE VIEW `plb_kj_product_details` AS
SELECT
    p.id,
    p.name,
    p.name_en,
    p.description,
    p.sku,
    p.price,
    p.compare_price,
    p.inventory_quantity,
    p.status,
    p.is_featured,
    p.sales_count,
    p.rating_average,
    p.rating_count,
    c.name as category_name,
    c.name_en as category_name_en,
    (SELECT image_url FROM plb_kj_product_images pi WHERE pi.product_id = p.id AND pi.is_primary = 1 LIMIT 1) as primary_image,
    (SELECT COUNT(*) FROM plb_kj_product_variants pv WHERE pv.product_id = p.id AND pv.is_active = 1) as variant_count
FROM plb_kj_products p
LEFT JOIN plb_kj_categories c ON p.category_id = c.id;

-- ========================================
-- 9. 触发器创建
-- ========================================

-- 9.1 自动更新聊天会话最后消息时间
DROP TRIGGER IF EXISTS update_last_message_time;
DELIMITER //
CREATE TRIGGER update_last_message_time
AFTER INSERT ON plb_kj_chat_messages
FOR EACH ROW
BEGIN
    UPDATE plb_kj_chat_sessions
    SET last_message_at = NEW.created_at
    WHERE id = NEW.session_id;
END //
DELIMITER ;

-- 9.2 自动更新产品销量
DROP TRIGGER IF EXISTS update_product_sales;
DELIMITER //
CREATE TRIGGER update_product_sales
AFTER INSERT ON plb_kj_order_items
FOR EACH ROW
BEGIN
    UPDATE plb_kj_products
    SET sales_count = sales_count + NEW.quantity
    WHERE id = NEW.product_id;
END //
DELIMITER ;

SET FOREIGN_KEY_CHECKS = 1;

-- ========================================
-- 10. 安装验证
-- ========================================

SELECT 'PLB-KJ Complete Database Setup Completed Successfully!' as status;

-- 显示表统计信息
SELECT 'Table Statistics' as info;
SELECT 'Admin Users' as table_name, COUNT(*) as count FROM plb_kj_admin_users
UNION ALL SELECT 'Customers' as table_name, COUNT(*) as count FROM plb_kj_customers
UNION ALL SELECT 'Chat Sessions' as table_name, COUNT(*) as count FROM plb_kj_chat_sessions
UNION ALL SELECT 'Chat Messages' as table_name, COUNT(*) as count FROM plb_kj_chat_messages
UNION ALL SELECT 'Quick Replies' as table_name, COUNT(*) as count FROM plb_kj_chat_quick_replies
UNION ALL SELECT 'Categories' as table_name, COUNT(*) as count FROM plb_kj_categories
UNION ALL SELECT 'Products' as table_name, COUNT(*) as count FROM plb_kj_products
UNION ALL SELECT 'Currencies' as table_name, COUNT(*) as count FROM plb_kj_currencies
UNION ALL SELECT 'System Settings' as table_name, COUNT(*) as count FROM plb_kj_system_settings;

-- 显示聊天功能状态
SELECT 'Chat System Status' as info;
SELECT setting_key, setting_value
FROM plb_kj_system_settings
WHERE category = 'chat'
ORDER BY setting_key;
