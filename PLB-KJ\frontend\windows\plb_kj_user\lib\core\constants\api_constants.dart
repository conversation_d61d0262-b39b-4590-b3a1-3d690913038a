/// API常量配置
class ApiConstants {
  // 基础URL
  static const String baseUrl = 'http://localhost:8080/api';
  
  // 认证相关
  static const String customerLogin = '/customers/login';
  static const String customerRegister = '/customers/register';
  static const String validateToken = '/auth/validate';
  
  // 客户相关
  static const String customerProfile = '/customers/profile';
  static const String customerPassword = '/customers/password';
  
  // 商品相关
  static const String products = '/products';
  static const String productById = '/products/{id}';
  static const String categories = '/categories';
  static const String categoryById = '/categories/{id}';
  static const String categoryProducts = '/categories/{id}/products';
  static const String searchProducts = '/products/search';
  static const String featuredProducts = '/products/featured';
  static const String newProducts = '/products/new';
  static const String popularProducts = '/products/popular';
  
  // 购物车相关
  static const String cart = '/cart';
  static const String cartAdd = '/cart/add';
  static const String cartUpdate = '/cart/update';
  static const String cartRemove = '/cart/remove';
  static const String cartClear = '/cart/clear';
  
  // 收藏相关
  static const String favorites = '/favorites';
  static const String favoriteAdd = '/favorites/add';
  static const String favoriteRemove = '/favorites/remove';
  
  // 订单相关
  static const String orders = '/orders';
  static const String orderById = '/orders/{id}';
  static const String orderCreate = '/orders/create';
  static const String orderCancel = '/orders/{id}/cancel';
  
  // 地址相关
  static const String addresses = '/addresses';
  static const String addressById = '/addresses/{id}';
  static const String addressCreate = '/addresses/create';
  static const String addressUpdate = '/addresses/{id}/update';
  static const String addressDelete = '/addresses/{id}/delete';
  
  // 聊天系统
  static const String chatSessions = '/chat/sessions';
  static const String chatSessionById = '/chat/sessions/{id}';
  static const String chatMessages = '/chat/sessions/{id}/messages';
  static const String chatSendMessage = '/chat/sessions/{id}/messages';
  static const String chatCreateSession = '/chat/sessions/create';
  
  // 货币相关
  static const String currencies = '/currencies';
  
  // 文件上传
  static const String fileUpload = '/files/upload';
  
  // 健康检查
  static const String health = '/health';
  
  /// 替换URL中的参数
  static String replaceUrlParams(String url, Map<String, dynamic> params) {
    String result = url;
    params.forEach((key, value) {
      result = result.replaceAll('{$key}', value.toString());
    });
    return result;
  }
}

/// HTTP状态码
class HttpStatus {
  static const int ok = 200;
  static const int created = 201;
  static const int noContent = 204;
  static const int badRequest = 400;
  static const int unauthorized = 401;
  static const int forbidden = 403;
  static const int notFound = 404;
  static const int conflict = 409;
  static const int internalServerError = 500;
}

/// API响应状态
class ApiResponseStatus {
  static const String success = 'success';
  static const String error = 'error';
  static const String warning = 'warning';
}

/// 应用常量
class AppConstants {
  // 应用信息
  static const String appName = 'PLB-KJ 跨境电商';
  static const String appVersion = '1.0.0';
  
  // 分页
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // 缓存键
  static const String cacheKeyProducts = 'cache_products';
  static const String cacheKeyCategories = 'cache_categories';
  static const String cacheKeyCart = 'cache_cart';
  static const String cacheKeyFavorites = 'cache_favorites';
  
  // 本地存储键
  static const String storageKeyAuthToken = 'auth_token';
  static const String storageKeyCurrentUser = 'current_user';
  static const String storageKeyLanguage = 'language';
  static const String storageKeyCurrency = 'currency';
  static const String storageKeyTheme = 'theme';
  
  // 默认值
  static const String defaultLanguage = 'zh-CN';
  static const String defaultCurrency = 'USD';
  
  // 图片占位符
  static const String placeholderImage = 'assets/images/placeholder.png';
  static const String placeholderAvatar = 'assets/images/avatar_placeholder.png';
  
  // 支持的语言
  static const Map<String, String> supportedLanguages = {
    'zh-CN': '简体中文',
    'en-US': 'English',
    'ja-JP': '日本語',
    'ko-KR': '한국어',
  };
  
  // 支持的货币
  static const Map<String, String> supportedCurrencies = {
    'USD': 'US Dollar',
    'CNY': 'Chinese Yuan',
    'EUR': 'Euro',
    'JPY': 'Japanese Yen',
    'KRW': 'Korean Won',
  };
}
