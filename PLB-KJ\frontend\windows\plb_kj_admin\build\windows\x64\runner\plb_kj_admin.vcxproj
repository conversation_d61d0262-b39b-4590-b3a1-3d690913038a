﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Profile|x64">
      <Configuration>Profile</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{CA40B1B1-7AE8-3332-8A79-70F1D85449B2}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>plb_kj_admin</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\build\windows\x64\runner\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">plb_kj_admin.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">plb_kj_admin</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\build\windows\x64\runner\Profile\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">plb_kj_admin.dir\Profile\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">plb_kj_admin</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\build\windows\x64\runner\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">plb_kj_admin.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">plb_kj_admin</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\.plugin_symlinks\screen_retriever\windows\include;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\.plugin_symlinks\window_manager\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_HAS_EXCEPTIONS=0;_DEBUG;FLUTTER_VERSION="1.0.0+1";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_HAS_EXCEPTIONS=0;FLUTTER_VERSION=\"1.0.0+1\";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\.plugin_symlinks\screen_retriever\windows\include;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\.plugin_symlinks\window_manager\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\.plugin_symlinks\screen_retriever\windows\include;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\.plugin_symlinks\window_manager\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\flutter\Debug\flutter_wrapper_app.lib;dwmapi.lib;..\plugins\file_selector_windows\Debug\file_selector_windows_plugin.lib;..\plugins\screen_retriever\Debug\screen_retriever_plugin.lib;..\plugins\window_manager\Debug\window_manager_plugin.lib;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\flutter_windows.dll.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/build/windows/x64/runner/Debug/plb_kj_admin.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/build/windows/x64/runner/Debug/plb_kj_admin.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <Manifest>
      <AdditionalManifestFiles>D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\runner\runner.exe.manifest;</AdditionalManifestFiles>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\.plugin_symlinks\screen_retriever\windows\include;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\.plugin_symlinks\window_manager\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION="1.0.0+1";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR="Profile"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION=\"1.0.0+1\";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR=\"Profile\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\.plugin_symlinks\screen_retriever\windows\include;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\.plugin_symlinks\window_manager\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\.plugin_symlinks\screen_retriever\windows\include;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\.plugin_symlinks\window_manager\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\flutter\Profile\flutter_wrapper_app.lib;dwmapi.lib;..\plugins\file_selector_windows\Profile\file_selector_windows_plugin.lib;..\plugins\screen_retriever\Profile\screen_retriever_plugin.lib;..\plugins\window_manager\Profile\window_manager_plugin.lib;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\flutter_windows.dll.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/build/windows/x64/runner/Profile/plb_kj_admin.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/build/windows/x64/runner/Profile/plb_kj_admin.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <Manifest>
      <AdditionalManifestFiles>D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\runner\runner.exe.manifest;</AdditionalManifestFiles>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\.plugin_symlinks\screen_retriever\windows\include;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\.plugin_symlinks\window_manager\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION="1.0.0+1";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION=\"1.0.0+1\";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\.plugin_symlinks\screen_retriever\windows\include;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\.plugin_symlinks\window_manager\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\cpp_client_wrapper\include;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\.plugin_symlinks\screen_retriever\windows\include;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\.plugin_symlinks\window_manager\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\flutter\Release\flutter_wrapper_app.lib;dwmapi.lib;..\plugins\file_selector_windows\Release\file_selector_windows_plugin.lib;..\plugins\screen_retriever\Release\screen_retriever_plugin.lib;..\plugins\window_manager\Release\window_manager_plugin.lib;D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\ephemeral\flutter_windows.dll.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/build/windows/x64/runner/Release/plb_kj_admin.lib</ImportLibrary>
      <ProgramDataBaseFile>D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/build/windows/x64/runner/Release/plb_kj_admin.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <Manifest>
      <AdditionalManifestFiles>D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\runner\runner.exe.manifest;</AdditionalManifestFiles>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\runner\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows/runner/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows -BD:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/build/windows/x64 --check-stamp-file D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/build/windows/x64/runner/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\build\windows\x64\runner\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">Building Custom Rule D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows/runner/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows -BD:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/build/windows/x64 --check-stamp-file D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/build/windows/x64/runner/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\build\windows\x64\runner\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows/runner/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows -BD:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/build/windows/x64 --check-stamp-file D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/build/windows/x64/runner/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\build\windows\x64\runner\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\runner\flutter_window.cpp" />
    <ClCompile Include="D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\runner\main.cpp" />
    <ClCompile Include="D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\runner\utils.cpp" />
    <ClCompile Include="D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\runner\win32_window.cpp" />
    <ClCompile Include="D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\flutter\generated_plugin_registrant.cc" />
    <ResourceCompile Include="D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\windows\runner\Runner.rc" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\build\windows\x64\ZERO_CHECK.vcxproj">
      <Project>{ADE84548-982A-304D-ACD9-E4316252A20D}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\build\windows\x64\plugins\file_selector_windows\file_selector_windows_plugin.vcxproj">
      <Project>{2AD0A065-5E30-3639-8B0E-2AB56E228DA0}</Project>
      <Name>file_selector_windows_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\build\windows\x64\flutter\flutter_assemble.vcxproj">
      <Project>{5EB28978-D6CB-3660-8D94-9E4A2DC0EB8C}</Project>
      <Name>flutter_assemble</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\build\windows\x64\flutter\flutter_wrapper_app.vcxproj">
      <Project>{B92E5ED6-906C-3B47-ACD7-48D2B33A16F9}</Project>
      <Name>flutter_wrapper_app</Name>
    </ProjectReference>
    <ProjectReference Include="D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\build\windows\x64\plugins\screen_retriever\screen_retriever_plugin.vcxproj">
      <Project>{8EC49990-0BDF-3D2A-8A4F-71410F733CDE}</Project>
      <Name>screen_retriever_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="D:\PLB-Links\PLB-KJ\frontend\windows\plb_kj_admin\build\windows\x64\plugins\window_manager\window_manager_plugin.vcxproj">
      <Project>{E44C40E5-D06C-3102-AE98-D42A20F885BE}</Project>
      <Name>window_manager_plugin</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>