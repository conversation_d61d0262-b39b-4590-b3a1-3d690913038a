<?php

// API路由文件

use App\Controllers\UserController;
use App\Controllers\CustomerController;
use App\Controllers\AdminController;
use App\Controllers\StatsController;
use App\Controllers\SystemSettingsController;
use App\Controllers\ChatController;
use App\Routing\Router;
use App\Middleware\AuthMiddleware;
use App\Middleware\CorsMiddleware;
use App\Helpers\Database;

// 应用全局中间件
Router::use([CorsMiddleware::class, 'handle']);

// 公开路由（无需认证）
Router::get('/health', function() {
    try {
        // 检查数据库连接
        $db = Database::getInstance();
        $stmt = $db->query("SELECT 1");
        $result = $stmt->fetch();
        
        if ($result) {
            http_response_code(200);
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'message' => '服务健康',
                'data' => [
                    'status' => 'healthy',
                    'database' => 'connected',
                    'timestamp' => time(),
                    'datetime' => date('Y-m-d H:i:s')
                ]
            ]);
        } else {
            http_response_code(503);
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => '数据库连接失败',
                'data' => [
                    'status' => 'unhealthy',
                    'database' => 'disconnected',
                    'timestamp' => time(),
                    'datetime' => date('Y-m-d H:i:s')
                ]
            ]);
        }
    } catch (Exception $e) {
        http_response_code(503);
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => '服务不健康: ' . $e->getMessage(),
            'data' => [
                'status' => 'unhealthy',
                'database' => 'error',
                'timestamp' => time(),
                'datetime' => date('Y-m-d H:i:s')
            ]
        ]);
    }
    exit;
});
Router::post('/admin/login', [AdminController::class, 'login']);
Router::post('/users/login', [UserController::class, 'login']);
Router::post('/customers/login', [CustomerController::class, 'login']);
Router::post('/customers/register', [CustomerController::class, 'register']);
Router::get('/auth/captcha', [AdminController::class, 'getCaptcha']);
Router::get('/auth/validate', [AdminController::class, 'validateToken']);

// 受保护路由组（需要认证）
Router::group(['middleware' => [AuthMiddleware::class, 'handle']], function() {
    // 普通用户路由
    Router::get('/users/profile', [UserController::class, 'getProfile']);

    // 客户路由
    Router::get('/customers/profile', [CustomerController::class, 'getProfile']);
    Router::put('/customers/profile', [CustomerController::class, 'updateProfile']);
    Router::put('/customers/password', [CustomerController::class, 'changePassword']);

    // 管理员用户管理路由
    Router::get('/admin/users', [UserController::class, 'getAllForAdmin']);
    Router::post('/admin/users', [UserController::class, 'createForAdmin']);
    Router::get('/admin/users/:id', [UserController::class, 'getByIdForAdmin']);
    Router::put('/admin/users/:id', [UserController::class, 'updateForAdmin']);
    Router::delete('/admin/users/:id', [UserController::class, 'deleteForAdmin']);
    Router::post('/admin/users/batch', [UserController::class, 'batchDeleteForAdmin']);
    Router::put('/admin/users/:id/status', [UserController::class, 'updateStatusForAdmin']);
    Router::put('/admin/users/:id/password', [UserController::class, 'resetPasswordForAdmin']);

    // 统计数据路由
    Router::get('/admin/stats', [StatsController::class, 'getStats']);
    Router::get('/admin/stats/users', [StatsController::class, 'getUserStats']);
    Router::get('/stats/:type', [StatsController::class, 'getStatsByType']);

    // 聊天功能路由
    Router::get('/chat/sessions', [ChatController::class, 'getSessions']);
    Router::post('/chat/sessions', [ChatController::class, 'createSession']);
    Router::get('/chat/sessions/:id/messages', [ChatController::class, 'getSessionMessages']);
    Router::post('/chat/sessions/:id/messages', [ChatController::class, 'sendMessage']);
    Router::put('/chat/sessions/:id/accept', [ChatController::class, 'acceptSession']);
    Router::put('/chat/sessions/:id/close', [ChatController::class, 'closeSession']);
    Router::put('/chat/sessions/:id/read', [ChatController::class, 'markMessagesRead']);
    Router::get('/chat/stats', [ChatController::class, 'getChatStats']);

// 系统设置路由
Router::group(['middleware' => [AuthMiddleware::class, 'handle']], function() {
    Router::get('/settings/background', [SystemSettingsController::class, 'getBackgroundSetting']);
    Router::post('/settings/background', [SystemSettingsController::class, 'updateBackgroundSetting']);
        
    // 新增的后台管理页面路由
    Router::get('/admin/settings/background', [SystemSettingsController::class, 'showBackgroundSettingsPage']);
});
});