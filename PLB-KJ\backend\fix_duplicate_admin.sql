-- 修复重复管理员用户问题的脚本

-- 方案1：如果想保留现有的admin用户，只需要跳过插入
-- 不需要执行任何操作，数据库已经有admin用户了

-- 方案2：如果想更新现有admin用户的信息
UPDATE plb_kj_admin_users 
SET 
    email = '<EMAIL>',
    first_name = '系统',
    last_name = '管理员',
    role = 'super_admin',
    status = 1,
    updated_at = NOW()
WHERE username = 'admin';

-- 方案3：如果想删除现有admin用户并重新创建（谨慎使用）
-- DELETE FROM plb_kj_admin_users WHERE username = 'admin';
-- INSERT INTO plb_kj_admin_users (username, email, password_hash, first_name, last_name, role, status) VALUES
-- ('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '系统', '管理员', 'super_admin', 1);

-- 检查admin用户是否存在
SELECT id, username, email, first_name, last_name, role, status, created_at 
FROM plb_kj_admin_users 
WHERE username = 'admin';
