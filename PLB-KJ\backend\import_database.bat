@echo off
echo ========================================
echo PLB-KJ 数据库导入脚本 (MySQL 5.7 兼容版)
echo ========================================
echo.

set /p DB_HOST="请输入MySQL主机地址 (默认: localhost): "
if "%DB_HOST%"=="" set DB_HOST=localhost

set /p DB_PORT="请输入MySQL端口 (默认: 3306): "
if "%DB_PORT%"=="" set DB_PORT=3306

set /p DB_USER="请输入MySQL用户名 (默认: root): "
if "%DB_USER%"=="" set DB_USER=root

set /p DB_PASS="请输入MySQL密码: "

set /p DB_NAME="请输入数据库名称 (默认: plb_kj_ecommerce): "
if "%DB_NAME%"=="" set DB_NAME=plb_kj_ecommerce

echo.
echo 正在连接到MySQL服务器...
echo 主机: %DB_HOST%:%DB_PORT%
echo 用户: %DB_USER%
echo 数据库: %DB_NAME%
echo.

REM 检查数据库是否存在，如果不存在则创建
echo 检查并创建数据库...
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASS% -e "CREATE DATABASE IF NOT EXISTS %DB_NAME% CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

if %ERRORLEVEL% NEQ 0 (
    echo 错误: 无法连接到MySQL服务器或创建数据库
    pause
    exit /b 1
)

echo 数据库创建成功！
echo.

echo 正在导入数据库结构和数据...
mysql -h%DB_HOST% -P%DB_PORT% -u%DB_USER% -p%DB_PASS% %DB_NAME% < plb_kj_database_mysql57.sql

if %ERRORLEVEL% NEQ 0 (
    echo 错误: 数据库导入失败
    pause
    exit /b 1
)

echo.
echo ========================================
echo 数据库导入完成！
echo ========================================
echo.
echo 默认管理员账户:
echo 用户名: admin
echo 密码: password (请登录后立即修改)
echo 邮箱: <EMAIL>
echo.
echo 数据库连接信息:
echo 主机: %DB_HOST%:%DB_PORT%
echo 数据库: %DB_NAME%
echo.
echo 请更新后端配置文件中的数据库连接信息
echo.
pause
