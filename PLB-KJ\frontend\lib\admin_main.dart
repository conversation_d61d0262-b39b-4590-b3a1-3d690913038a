import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'shared/routes/admin_routes.dart';
import 'shared/theme/app_theme.dart';
import 'features/chat/chat_service.dart';

void main() {
  runApp(const AdminApp());
}

class AdminApp extends StatelessWidget {
  const AdminApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ChatService()),
      ],
      child: MaterialApp(
        title: '跨境电商管理系统 - 管理端',
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: ThemeMode.system,
        onGenerateRoute: AdminRoutes.generateRoute,
        initialRoute: AdminRoutes.splash,
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}
