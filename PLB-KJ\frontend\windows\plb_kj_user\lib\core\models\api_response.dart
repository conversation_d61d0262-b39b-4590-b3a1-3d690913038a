import 'package:json_annotation/json_annotation.dart';

part 'api_response.g.dart';

/// 通用API响应模型
@JsonSerializable(genericArgumentFactories: true)
class ApiResponse<T> {
  final bool success;
  final String? message;
  final T? data;
  final String? error;
  final int? code;

  const ApiResponse({
    required this.success,
    this.message,
    this.data,
    this.error,
    this.code,
  });

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$ApiResponseFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$ApiResponseToJson(this, toJsonT);

  /// 创建成功响应
  factory ApiResponse.success({
    T? data,
    String? message,
  }) {
    return ApiResponse<T>(
      success: true,
      data: data,
      message: message,
    );
  }

  /// 创建错误响应
  factory ApiResponse.error({
    String? error,
    String? message,
    int? code,
  }) {
    return ApiResponse<T>(
      success: false,
      error: error,
      message: message,
      code: code,
    );
  }

  /// 是否成功
  bool get isSuccess => success;

  /// 是否失败
  bool get isError => !success;

  /// 获取错误信息
  String get errorMessage => error ?? message ?? '未知错误';
}

/// 分页响应模型
@JsonSerializable(genericArgumentFactories: true)
class PaginatedResponse<T> {
  final List<T> data;
  final int total;
  final int page;
  @JsonKey(name: 'page_size')
  final int pageSize;
  @JsonKey(name: 'total_pages')
  final int totalPages;

  const PaginatedResponse({
    required this.data,
    required this.total,
    required this.page,
    required this.pageSize,
    required this.totalPages,
  });

  factory PaginatedResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$PaginatedResponseFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$PaginatedResponseToJson(this, toJsonT);

  /// 是否有下一页
  bool get hasNextPage => page < totalPages;

  /// 是否有上一页
  bool get hasPreviousPage => page > 1;

  /// 是否为空
  bool get isEmpty => data.isEmpty;

  /// 是否不为空
  bool get isNotEmpty => data.isNotEmpty;
}

/// 简单列表响应模型
@JsonSerializable(genericArgumentFactories: true)
class ListResponse<T> {
  final List<T> items;
  final int count;

  const ListResponse({
    required this.items,
    required this.count,
  });

  factory ListResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$ListResponseFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$ListResponseToJson(this, toJsonT);

  /// 是否为空
  bool get isEmpty => items.isEmpty;

  /// 是否不为空
  bool get isNotEmpty => items.isNotEmpty;
}
