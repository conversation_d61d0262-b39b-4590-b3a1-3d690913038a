-- 创建系统设置表的独立脚本
-- 如果遇到表不存在的错误，可以单独运行这个脚本

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 创建系统设置表
CREATE TABLE IF NOT EXISTS `plb_kj_system_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL COMMENT '设置键名',
  `setting_value` text DEFAULT NULL COMMENT '设置值',
  `setting_type` enum('string','number','boolean','json') NOT NULL DEFAULT 'string' COMMENT '设置类型',
  `category` varchar(50) NOT NULL DEFAULT 'general' COMMENT '设置分类',
  `description` varchar(255) DEFAULT NULL COMMENT '设置描述',
  `is_public` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否公开',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`),
  KEY `idx_category` (`category`),
  KEY `idx_is_public` (`is_public`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统设置表';

-- 插入默认系统设置
INSERT IGNORE INTO `plb_kj_system_settings` (`setting_key`, `setting_value`, `setting_type`, `category`, `description`, `is_public`) VALUES
('site_name', 'PLB-KJ跨境电商管理系统', 'string', 'general', '网站名称', 1),
('site_email', '<EMAIL>', 'string', 'general', '网站邮箱', 0),
('site_url', 'http://localhost/PLB-KJ', 'string', 'general', '网站URL', 1),
('currency_default', 'USD', 'string', 'general', '默认货币', 1),
('tax_rate', '0.00', 'number', 'general', '默认税率', 1),
('timezone', 'Asia/Shanghai', 'string', 'general', '时区设置', 1),
('language_default', 'zh-CN', 'string', 'general', '默认语言', 1),

-- 聊天系统设置
('chat_enabled', '1', 'boolean', 'chat', '是否启用聊天功能', 1),
('chat_max_sessions_per_agent', '5', 'number', 'chat', '每个客服最大并发会话数', 0),
('chat_auto_assign', '1', 'boolean', 'chat', '是否自动分配客服', 0),
('chat_session_timeout', '1800', 'number', 'chat', '会话超时时间(秒)', 0),
('chat_file_upload_enabled', '1', 'boolean', 'chat', '是否允许文件上传', 0),
('chat_file_max_size', '10485760', 'number', 'chat', '文件上传最大大小(字节)', 0),
('chat_allowed_file_types', 'jpg,jpeg,png,gif,pdf,doc,docx,txt', 'string', 'chat', '允许的文件类型', 0),

-- 邮件设置
('mail_driver', 'smtp', 'string', 'mail', '邮件驱动', 0),
('mail_host', 'smtp.gmail.com', 'string', 'mail', 'SMTP主机', 0),
('mail_port', '587', 'number', 'mail', 'SMTP端口', 0),
('mail_username', '', 'string', 'mail', 'SMTP用户名', 0),
('mail_password', '', 'string', 'mail', 'SMTP密码', 0),
('mail_encryption', 'tls', 'string', 'mail', '加密方式', 0),
('mail_from_address', '<EMAIL>', 'string', 'mail', '发件人邮箱', 0),
('mail_from_name', 'PLB-KJ系统', 'string', 'mail', '发件人名称', 0),

-- 支付设置
('payment_enabled', '1', 'boolean', 'payment', '是否启用支付功能', 1),
('payment_currency', 'USD', 'string', 'payment', '支付货币', 1),
('payment_methods', '["credit_card","paypal","bank_transfer"]', 'json', 'payment', '支持的支付方式', 1),

-- 安全设置
('security_password_min_length', '8', 'number', 'security', '密码最小长度', 0),
('security_login_attempts', '5', 'number', 'security', '登录尝试次数限制', 0),
('security_lockout_duration', '900', 'number', 'security', '账户锁定时长(秒)', 0),
('security_session_lifetime', '7200', 'number', 'security', '会话生命周期(秒)', 0),

-- 文件上传设置
('upload_max_file_size', '********', 'number', 'upload', '最大文件上传大小(字节)', 0),
('upload_allowed_types', 'jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,txt,zip', 'string', 'upload', '允许的文件类型', 0),
('upload_path', 'uploads/', 'string', 'upload', '上传文件路径', 0),

-- API设置
('api_rate_limit', '1000', 'number', 'api', 'API请求频率限制(每小时)', 0),
('api_timeout', '30', 'number', 'api', 'API请求超时时间(秒)', 0),

-- 缓存设置
('cache_enabled', '1', 'boolean', 'cache', '是否启用缓存', 0),
('cache_driver', 'file', 'string', 'cache', '缓存驱动', 0),
('cache_ttl', '3600', 'number', 'cache', '缓存生存时间(秒)', 0),

-- 日志设置
('log_level', 'info', 'string', 'log', '日志级别', 0),
('log_max_files', '30', 'number', 'log', '日志文件保留天数', 0),
('log_channel', 'daily', 'string', 'log', '日志通道', 0);

-- 验证数据是否插入成功
SELECT COUNT(*) as total_settings FROM plb_kj_system_settings;
SELECT category, COUNT(*) as count FROM plb_kj_system_settings GROUP BY category;

SET FOREIGN_KEY_CHECKS = 1;
