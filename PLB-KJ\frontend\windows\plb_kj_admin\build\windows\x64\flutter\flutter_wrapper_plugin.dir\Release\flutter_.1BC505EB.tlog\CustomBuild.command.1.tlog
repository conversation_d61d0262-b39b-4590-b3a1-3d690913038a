^D:\PLB-LINKS\PLB-KJ\FRONTEND\WINDOWS\PLB_KJ_ADMIN\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/windows -BD:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/build/windows/x64 --check-stamp-file D:/PLB-Links/PLB-KJ/frontend/windows/plb_kj_admin/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
