# Generated code do not commit.
file(TO_CMAKE_PATH "D:\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\PLB-Links\\PLB-KJ\\frontend\\plb_kj_admin" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=D:\\flutter"
  "PROJECT_DIR=D:\\PLB-Links\\PLB-KJ\\frontend\\plb_kj_admin"
  "FLUTTER_ROOT=D:\\flutter"
  "FLUTTER_EPHEMERAL_DIR=D:\\PLB-Links\\PLB-KJ\\frontend\\plb_kj_admin\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\PLB-Links\\PLB-KJ\\frontend\\plb_kj_admin"
  "FLUTTER_TARGET=lib\\main.dart"
  "DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzUuMC0wLjEucHJl,RkxVVFRFUl9DSEFOTkVMPWJldGE=,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049YzBmMmExZGQ2MA==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049MWM5YzIwZTdjMw==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My45LjAgKGJ1aWxkIDMuOS4wLTMzMy4yLmJldGEp"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=true"
  "PACKAGE_CONFIG=D:\\PLB-Links\\PLB-KJ\\frontend\\plb_kj_admin\\.dart_tool\\package_config.json"
)
