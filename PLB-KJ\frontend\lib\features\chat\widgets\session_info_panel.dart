import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/chat_models.dart';

class SessionInfoPanel extends StatelessWidget {
  final ChatSession session;

  const SessionInfoPanel({
    Key? key,
    required this.session,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          const Text(
            '会话信息',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // 客户信息
          _buildSection(
            title: '客户信息',
            child: _buildCustomerInfo(),
          ),

          const SizedBox(height: 16),

          // 会话详情
          _buildSection(
            title: '会话详情',
            child: _buildSessionDetails(),
          ),

          const SizedBox(height: 16),

          // 客服信息
          if (session.admin != null)
            _buildSection(
              title: '客服信息',
              child: _buildAdminInfo(),
            ),

          const SizedBox(height: 16),

          // 评价信息
          if (session.customerSatisfactionRating != null)
            _buildSection(
              title: '客户评价',
              child: _buildRatingInfo(),
            ),
        ],
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required Widget child,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: child,
        ),
      ],
    );
  }

  Widget _buildCustomerInfo() {
    final customer = session.customer;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            CircleAvatar(
              radius: 20,
              backgroundColor: Colors.blue[100],
              backgroundImage: customer?.avatar != null
                  ? NetworkImage(customer!.avatar!)
                  : null,
              child: customer?.avatar == null
                  ? Text(
                      customer?.fullName.substring(0, 1).toUpperCase() ?? 'C',
                      style: TextStyle(
                        color: Colors.blue[800],
                        fontWeight: FontWeight.bold,
                      ),
                    )
                  : null,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    customer?.fullName ?? '客户 ${session.customerId}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  if (customer?.email != null)
                    Text(
                      customer!.email,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                    ),
                ],
              ),
            ),
            // 在线状态
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: customer?.isOnline == true ? Colors.green : Colors.grey,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                customer?.isOnline == true ? '在线' : '离线',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        if (customer?.phone != null) ...[
          const SizedBox(height: 8),
          Row(
            children: [
              const Icon(Icons.phone, size: 16, color: Colors.grey),
              const SizedBox(width: 8),
              Text(
                customer!.phone!,
                style: const TextStyle(fontSize: 14),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildSessionDetails() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildDetailRow('会话ID', session.sessionId),
        _buildDetailRow('标题', session.title),
        _buildDetailRow('状态', _getStatusText(session.status)),
        _buildDetailRow('优先级', _getPriorityText(session.priority)),
        _buildDetailRow('分类', _getCategoryText(session.category)),
        _buildDetailRow('开始时间', DateFormat('yyyy-MM-dd HH:mm:ss').format(session.startedAt)),
        _buildDetailRow('最后消息', DateFormat('yyyy-MM-dd HH:mm:ss').format(session.lastMessageAt)),
        if (session.endedAt != null)
          _buildDetailRow('结束时间', DateFormat('yyyy-MM-dd HH:mm:ss').format(session.endedAt!)),
        if (session.unreadCount > 0)
          _buildDetailRow('未读消息', '${session.unreadCount} 条'),
      ],
    );
  }

  Widget _buildAdminInfo() {
    final admin = session.admin!;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            CircleAvatar(
              radius: 16,
              backgroundColor: Colors.green[100],
              backgroundImage: admin.avatar != null
                  ? NetworkImage(admin.avatar!)
                  : null,
              child: admin.avatar == null
                  ? Text(
                      admin.fullName.substring(0, 1).toUpperCase(),
                      style: TextStyle(
                        color: Colors.green[800],
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    )
                  : null,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    admin.fullName,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                  Text(
                    admin.email,
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        _buildDetailRow('角色', _getRoleText(admin.role)),
        _buildDetailRow('状态', admin.isOnline ? '在线' : '离线'),
      ],
    );
  }

  Widget _buildRatingInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text('评分: '),
            ...List.generate(5, (index) {
              return Icon(
                index < (session.customerSatisfactionRating ?? 0)
                    ? Icons.star
                    : Icons.star_border,
                color: Colors.amber,
                size: 16,
              );
            }),
            const SizedBox(width: 8),
            Text('${session.customerSatisfactionRating}/5'),
          ],
        ),
        if (session.customerFeedback != null && session.customerFeedback!.isNotEmpty) ...[
          const SizedBox(height: 8),
          const Text(
            '反馈:',
            style: TextStyle(fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 4),
          Text(
            session.customerFeedback!,
            style: const TextStyle(fontSize: 14),
          ),
        ],
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 12,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'waiting':
        return '等待中';
      case 'active':
        return '进行中';
      case 'closed':
        return '已结束';
      default:
        return '未知';
    }
  }

  String _getPriorityText(String priority) {
    switch (priority) {
      case 'low':
        return '低';
      case 'normal':
        return '普通';
      case 'high':
        return '高';
      case 'urgent':
        return '紧急';
      default:
        return '普通';
    }
  }

  String _getCategoryText(String category) {
    switch (category) {
      case 'general':
        return '一般咨询';
      case 'technical':
        return '技术支持';
      case 'billing':
        return '账单问题';
      case 'complaint':
        return '投诉建议';
      default:
        return '一般咨询';
    }
  }

  String _getRoleText(String role) {
    switch (role) {
      case 'customer_service':
        return '客服';
      case 'supervisor':
        return '主管';
      case 'admin':
        return '管理员';
      default:
        return '客服';
    }
  }
}
