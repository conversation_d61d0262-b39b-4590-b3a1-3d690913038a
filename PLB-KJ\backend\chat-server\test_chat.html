<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PLB-KJ 聊天测试</title>
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .chat-box {
            height: 400px;
            border: 1px solid #ddd;
            padding: 10px;
            overflow-y: auto;
            background: #fafafa;
            margin: 10px 0;
        }
        .message {
            margin: 5px 0;
            padding: 8px 12px;
            border-radius: 18px;
            max-width: 70%;
        }
        .message.sent {
            background: #007bff;
            color: white;
            margin-left: auto;
            text-align: right;
        }
        .message.received {
            background: #e9ecef;
            color: #333;
        }
        .input-group {
            display: flex;
            gap: 10px;
            margin: 10px 0;
        }
        input, button, select {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        input[type="text"] {
            flex: 1;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .session-info {
            background: #e7f3ff;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>PLB-KJ 聊天系统测试</h1>
        
        <!-- 连接状态 -->
        <div id="status" class="status disconnected">未连接</div>
        
        <!-- 登录表单 -->
        <div id="loginForm">
            <h3>登录测试</h3>
            <div class="form-group">
                <label>用户类型:</label>
                <select id="userType">
                    <option value="admin">管理员</option>
                    <option value="customer">客户</option>
                </select>
            </div>
            <div class="form-group">
                <label>用户ID:</label>
                <input type="number" id="userId" value="1" min="1">
            </div>
            <div class="form-group">
                <label>JWT Token (可选):</label>
                <input type="text" id="token" placeholder="留空将使用测试token">
            </div>
            <button onclick="connect()">连接聊天服务器</button>
        </div>
        
        <!-- 会话管理 -->
        <div id="sessionManagement" style="display: none;">
            <h3>会话管理</h3>
            <div class="input-group">
                <input type="text" id="sessionTitle" placeholder="会话标题" value="测试会话">
                <select id="sessionCategory">
                    <option value="general">一般咨询</option>
                    <option value="technical">技术支持</option>
                    <option value="billing">账单问题</option>
                    <option value="complaint">投诉建议</option>
                </select>
                <select id="sessionPriority">
                    <option value="normal">普通</option>
                    <option value="high">高</option>
                    <option value="urgent">紧急</option>
                </select>
                <button onclick="createSession()">创建会话</button>
            </div>
            
            <div id="sessionInfo" class="session-info" style="display: none;"></div>
        </div>
        
        <!-- 聊天界面 -->
        <div id="chatInterface" style="display: none;">
            <h3>聊天界面</h3>
            <div id="chatBox" class="chat-box"></div>
            
            <div class="input-group">
                <input type="text" id="messageInput" placeholder="输入消息..." onkeypress="handleKeyPress(event)">
                <select id="messageType">
                    <option value="text">文本</option>
                    <option value="image">图片</option>
                    <option value="file">文件</option>
                </select>
                <button onclick="sendMessage()">发送</button>
            </div>
            
            <div class="input-group">
                <button onclick="acceptSession()" id="acceptBtn" style="display: none;">接受会话</button>
                <button onclick="closeSession()" id="closeBtn" style="display: none;">关闭会话</button>
                <button onclick="disconnect()">断开连接</button>
            </div>
        </div>
    </div>

    <script>
        let socket = null;
        let currentSession = null;
        let userInfo = null;

        function connect() {
            const userType = document.getElementById('userType').value;
            const userId = document.getElementById('userId').value;
            let token = document.getElementById('token').value;

            // 如果没有提供token，生成一个测试token
            if (!token) {
                token = generateTestToken(userType, userId);
            }

            userInfo = { type: userType, id: userId };

            socket = io('http://localhost:3000', {
                auth: { token: token }
            });

            socket.on('connect', () => {
                updateStatus('已连接', true);
                document.getElementById('loginForm').style.display = 'none';
                document.getElementById('sessionManagement').style.display = 'block';
                addMessage('系统', '连接成功！', 'system');
            });

            socket.on('disconnect', () => {
                updateStatus('连接断开', false);
                addMessage('系统', '连接断开', 'system');
            });

            socket.on('connected', (data) => {
                addMessage('系统', `欢迎 ${data.user.first_name} ${data.user.last_name}！`, 'system');
                if (data.activeSessions && data.activeSessions.length > 0) {
                    currentSession = data.activeSessions[0];
                    showChatInterface();
                    updateSessionInfo();
                }
            });

            socket.on('new_message', (message) => {
                addMessage(message.senderName, message.content, 
                    message.senderType === userInfo.type ? 'sent' : 'received');
            });

            socket.on('session_created', (session) => {
                currentSession = session;
                showChatInterface();
                updateSessionInfo();
                addMessage('系统', '会话创建成功！', 'system');
            });

            socket.on('session_accepted', (data) => {
                addMessage('系统', `客服 ${data.agent.name} 已接入会话`, 'system');
                document.getElementById('acceptBtn').style.display = 'none';
            });

            socket.on('session_closed', (data) => {
                addMessage('系统', '会话已关闭', 'system');
                document.getElementById('closeBtn').style.display = 'none';
            });

            socket.on('new_waiting_session', (session) => {
                if (userInfo.type === 'admin') {
                    addMessage('系统', `新的等待会话: ${session.title}`, 'system');
                    currentSession = session;
                    showChatInterface();
                    updateSessionInfo();
                    document.getElementById('acceptBtn').style.display = 'inline-block';
                }
            });

            socket.on('user_typing', (data) => {
                if (data.userId != userInfo.id) {
                    addMessage('系统', `${data.userId} 正在输入...`, 'typing');
                }
            });

            socket.on('error', (error) => {
                addMessage('错误', error.message, 'error');
            });
        }

        function generateTestToken(userType, userId) {
            // 这是一个简化的测试token生成，实际应用中应该从后端获取
            const payload = {
                user_id: userId,
                user_type: userType,
                exp: Math.floor(Date.now() / 1000) + (60 * 60) // 1小时后过期
            };
            return btoa(JSON.stringify(payload)); // 简单的base64编码，仅用于测试
        }

        function createSession() {
            if (!socket) return;

            const title = document.getElementById('sessionTitle').value;
            const category = document.getElementById('sessionCategory').value;
            const priority = document.getElementById('sessionPriority').value;

            socket.emit('create_session', {
                title: title,
                category: category,
                priority: priority
            });
        }

        function sendMessage() {
            if (!socket || !currentSession) return;

            const input = document.getElementById('messageInput');
            const messageType = document.getElementById('messageType').value;
            const content = input.value.trim();

            if (!content) return;

            socket.emit('send_message', {
                sessionId: currentSession.id,
                content: content,
                messageType: messageType
            });

            input.value = '';
        }

        function acceptSession() {
            if (!socket || !currentSession) return;

            socket.emit('accept_session', {
                sessionId: currentSession.id
            });
        }

        function closeSession() {
            if (!socket || !currentSession) return;

            socket.emit('close_session', {
                sessionId: currentSession.id,
                rating: 5,
                feedback: '测试关闭'
            });
        }

        function disconnect() {
            if (socket) {
                socket.disconnect();
                socket = null;
            }
            currentSession = null;
            document.getElementById('loginForm').style.display = 'block';
            document.getElementById('sessionManagement').style.display = 'none';
            document.getElementById('chatInterface').style.display = 'none';
            updateStatus('未连接', false);
        }

        function updateStatus(text, connected) {
            const status = document.getElementById('status');
            status.textContent = text;
            status.className = `status ${connected ? 'connected' : 'disconnected'}`;
        }

        function showChatInterface() {
            document.getElementById('sessionManagement').style.display = 'none';
            document.getElementById('chatInterface').style.display = 'block';
        }

        function updateSessionInfo() {
            if (!currentSession) return;

            const info = document.getElementById('sessionInfo');
            info.innerHTML = `
                <strong>会话信息:</strong><br>
                ID: ${currentSession.id}<br>
                标题: ${currentSession.title}<br>
                状态: ${currentSession.status}<br>
                优先级: ${currentSession.priority}
            `;
            info.style.display = 'block';

            // 根据会话状态显示相应按钮
            if (currentSession.status === 'waiting' && userInfo.type === 'admin') {
                document.getElementById('acceptBtn').style.display = 'inline-block';
            }
            if (currentSession.status === 'active') {
                document.getElementById('closeBtn').style.display = 'inline-block';
            }
        }

        function addMessage(sender, content, type) {
            const chatBox = document.getElementById('chatBox');
            const message = document.createElement('div');
            message.className = `message ${type}`;
            
            if (type === 'system' || type === 'error' || type === 'typing') {
                message.innerHTML = `<em>${content}</em>`;
                message.style.background = type === 'error' ? '#f8d7da' : '#fff3cd';
                message.style.color = type === 'error' ? '#721c24' : '#856404';
                message.style.textAlign = 'center';
                message.style.margin = '10px auto';
            } else {
                message.innerHTML = `<strong>${sender}:</strong> ${content}`;
            }
            
            chatBox.appendChild(message);
            chatBox.scrollTop = chatBox.scrollHeight;

            // 清除输入状态消息
            if (type === 'typing') {
                setTimeout(() => {
                    if (message.parentNode) {
                        message.parentNode.removeChild(message);
                    }
                }, 3000);
            }
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }
    </script>
</body>
</html>
