import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../services/customer_chat_service.dart';
import '../models/chat_models.dart';

class CustomerChatScreen extends StatefulWidget {
  const CustomerChatScreen({Key? key}) : super(key: key);

  @override
  State<CustomerChatScreen> createState() => _CustomerChatScreenState();
}

class _CustomerChatScreenState extends State<CustomerChatScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  ChatSession? _currentSession;

  @override
  void initState() {
    super.initState();
    
    // 初始化聊天服务
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<CustomerChatService>().initialize();
    });
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('客服咨询'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
        actions: [
          Consumer<CustomerChatService>(
            builder: (context, chatService, child) {
              final unreadCount = chatService.getUnreadMessageCount();
              return Stack(
                children: [
                  IconButton(
                    onPressed: () {
                      chatService.refresh();
                    },
                    icon: const Icon(Icons.refresh),
                  ),
                  if (unreadCount > 0)
                    Positioned(
                      right: 8,
                      top: 8,
                      child: Container(
                        padding: const EdgeInsets.all(2),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        constraints: const BoxConstraints(
                          minWidth: 16,
                          minHeight: 16,
                        ),
                        child: Text(
                          '$unreadCount',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                ],
              );
            },
          ),
        ],
      ),
      body: Consumer<CustomerChatService>(
        builder: (context, chatService, child) {
          _currentSession = chatService.activeSession;
          
          if (!chatService.isConnected) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('正在连接客服系统...'),
                ],
              ),
            );
          }

          if (_currentSession == null) {
            return _buildStartChatArea();
          }

          return _buildChatArea(_currentSession!);
        },
      ),
    );
  }

  Widget _buildStartChatArea() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.support_agent,
              size: 80,
              color: Colors.blue[600],
            ),
            const SizedBox(height: 24),
            const Text(
              '需要帮助吗？',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '我们的客服团队随时为您提供帮助',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            
            // 咨询类型选择
            const Text(
              '请选择咨询类型：',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 16),
            
            Wrap(
              spacing: 12,
              runSpacing: 12,
              children: [
                _buildCategoryChip('一般咨询', 'general', Icons.help_outline),
                _buildCategoryChip('技术支持', 'technical', Icons.build),
                _buildCategoryChip('账单问题', 'billing', Icons.receipt),
                _buildCategoryChip('投诉建议', 'complaint', Icons.feedback),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryChip(String title, String category, IconData icon) {
    return ActionChip(
      avatar: Icon(icon, size: 18),
      label: Text(title),
      onPressed: () => _startChat(category, title),
      backgroundColor: Colors.blue[50],
      labelStyle: TextStyle(color: Colors.blue[800]),
    );
  }

  Widget _buildChatArea(ChatSession session) {
    return Column(
      children: [
        // 会话状态栏
        _buildSessionStatusBar(session),
        
        // 消息列表
        Expanded(
          child: _buildMessageList(session),
        ),
        
        // 消息输入框
        if (session.status != 'closed')
          _buildMessageInput(session),
        
        // 评价区域
        if (session.status == 'closed' && session.customerSatisfactionRating == null)
          _buildRatingArea(session),
      ],
    );
  }

  Widget _buildSessionStatusBar(ChatSession session) {
    Color statusColor;
    String statusText;
    IconData statusIcon;

    switch (session.status) {
      case 'waiting':
        statusColor = Colors.orange;
        statusText = '等待客服接入...';
        statusIcon = Icons.schedule;
        break;
      case 'active':
        statusColor = Colors.green;
        statusText = session.admin != null 
            ? '${session.admin!.fullName} 为您服务'
            : '客服已接入';
        statusIcon = Icons.chat;
        break;
      case 'closed':
        statusColor = Colors.grey;
        statusText = '会话已结束';
        statusIcon = Icons.check_circle;
        break;
      default:
        statusColor = Colors.grey;
        statusText = '未知状态';
        statusIcon = Icons.help;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.1),
        border: Border(
          bottom: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Row(
        children: [
          Icon(statusIcon, color: statusColor, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              statusText,
              style: TextStyle(
                color: statusColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          if (session.status == 'active')
            TextButton.icon(
              onPressed: () => _showCloseSessionDialog(session),
              icon: const Icon(Icons.close, size: 16),
              label: const Text('结束会话'),
              style: TextButton.styleFrom(
                foregroundColor: Colors.red,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildMessageList(ChatSession session) {
    return Consumer<CustomerChatService>(
      builder: (context, chatService, child) {
        final messages = chatService.messages[session.id.toString()] ?? [];
        
        if (messages.isEmpty) {
          // 自动加载消息
          WidgetsBinding.instance.addPostFrameCallback((_) {
            chatService.getSessionMessages(session.id);
          });
          
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        return ListView.builder(
          controller: _scrollController,
          padding: const EdgeInsets.all(16),
          itemCount: messages.length,
          itemBuilder: (context, index) {
            final message = messages[index];
            final isMe = message.senderType == 'customer';
            
            return Container(
              margin: const EdgeInsets.symmetric(vertical: 4),
              child: Row(
                mainAxisAlignment: isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
                children: [
                  if (!isMe) ...[
                    CircleAvatar(
                      radius: 16,
                      backgroundColor: Colors.blue[100],
                      child: Icon(
                        Icons.support_agent,
                        size: 16,
                        color: Colors.blue[800],
                      ),
                    ),
                    const SizedBox(width: 8),
                  ],
                  Flexible(
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                      decoration: BoxDecoration(
                        color: isMe ? Colors.blue[600] : Colors.grey[200],
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (!isMe)
                            Text(
                              message.senderName,
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: Colors.grey[700],
                              ),
                            ),
                          if (!isMe) const SizedBox(height: 4),
                          Text(
                            message.content,
                            style: TextStyle(
                              color: isMe ? Colors.white : Colors.black87,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            DateFormat('HH:mm').format(message.createdAt),
                            style: TextStyle(
                              fontSize: 10,
                              color: isMe ? Colors.white70 : Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  if (isMe) ...[
                    const SizedBox(width: 8),
                    CircleAvatar(
                      radius: 16,
                      backgroundColor: Colors.grey[300],
                      child: Icon(
                        Icons.person,
                        size: 16,
                        color: Colors.grey[700],
                      ),
                    ),
                  ],
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildMessageInput(ChatSession session) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _messageController,
              decoration: InputDecoration(
                hintText: '输入消息...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
              ),
              maxLines: 4,
              minLines: 1,
              onSubmitted: (_) => _sendMessage(session),
            ),
          ),
          const SizedBox(width: 8),
          Container(
            decoration: BoxDecoration(
              color: Colors.blue[600],
              shape: BoxShape.circle,
            ),
            child: IconButton(
              onPressed: () => _sendMessage(session),
              icon: const Icon(
                Icons.send,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRatingArea(ChatSession session) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        border: Border(top: BorderSide(color: Colors.blue[200]!)),
      ),
      child: Column(
        children: [
          const Text(
            '请为本次服务评分',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(5, (index) {
              return IconButton(
                onPressed: () => _rateSession(session, index + 1),
                icon: Icon(
                  Icons.star,
                  color: Colors.amber,
                  size: 32,
                ),
              );
            }),
          ),
        ],
      ),
    );
  }

  void _startChat(String category, String title) async {
    try {
      final session = await context.read<CustomerChatService>().createSession(
        title: title,
        category: category,
        priority: 'normal',
      );
      
      if (session != null) {
        setState(() {
          _currentSession = session;
        });
        
        // 发送欢迎消息
        await context.read<CustomerChatService>().sendMessage(
          sessionId: session.id,
          content: '您好，我需要关于"$title"的帮助。',
        );
        
        _scrollToBottom();
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('创建会话失败: $e')),
      );
    }
  }

  void _sendMessage(ChatSession session) async {
    final content = _messageController.text.trim();
    if (content.isEmpty) return;

    try {
      await context.read<CustomerChatService>().sendMessage(
        sessionId: session.id,
        content: content,
      );
      _messageController.clear();
      _scrollToBottom();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('发送消息失败: $e')),
      );
    }
  }

  void _showCloseSessionDialog(ChatSession session) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('结束会话'),
        content: const Text('确定要结束当前会话吗？结束后您可以对本次服务进行评价。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _closeSession(session);
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _closeSession(ChatSession session) async {
    try {
      await context.read<CustomerChatService>().closeSessionWithRating(
        sessionId: session.id,
        rating: 5, // 默认5星，用户可以后续修改
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('结束会话失败: $e')),
      );
    }
  }

  void _rateSession(ChatSession session, int rating) async {
    try {
      await context.read<CustomerChatService>().closeSessionWithRating(
        sessionId: session.id,
        rating: rating,
        feedback: '感谢您的服务！',
      );
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('评价已提交，感谢您的反馈！')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('提交评价失败: $e')),
      );
    }
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }
}
