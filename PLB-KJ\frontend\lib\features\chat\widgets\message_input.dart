import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

class MessageInput extends StatefulWidget {
  final TextEditingController controller;
  final Function(String, {String messageType}) onSend;
  final Function(bool) onTyping;
  final bool enabled;

  const MessageInput({
    Key? key,
    required this.controller,
    required this.onSend,
    required this.onTyping,
    this.enabled = true,
  }) : super(key: key);

  @override
  State<MessageInput> createState() => _MessageInputState();
}

class _MessageInputState extends State<MessageInput> {
  bool _isTyping = false;
  bool _showAttachmentOptions = false;
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    widget.controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    widget.controller.removeListener(_onTextChanged);
    _focusNode.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    final isTyping = widget.controller.text.isNotEmpty;
    if (_isTyping != isTyping) {
      _isTyping = isTyping;
      widget.onTyping(isTyping);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: Colors.grey[300]!),
        ),
      ),
      child: Column(
        children: [
          // 附件选项
          if (_showAttachmentOptions) _buildAttachmentOptions(),
          
          // 输入框
          Row(
            children: [
              // 附件按钮
              IconButton(
                onPressed: widget.enabled ? _toggleAttachmentOptions : null,
                icon: Icon(
                  _showAttachmentOptions ? Icons.close : Icons.attach_file,
                  color: widget.enabled ? Colors.grey[600] : Colors.grey[400],
                ),
                tooltip: '附件',
              ),
              
              // 文本输入框
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!),
                    borderRadius: BorderRadius.circular(24),
                  ),
                  child: TextField(
                    controller: widget.controller,
                    focusNode: _focusNode,
                    enabled: widget.enabled,
                    maxLines: 4,
                    minLines: 1,
                    decoration: const InputDecoration(
                      hintText: '输入消息...',
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                    ),
                    onSubmitted: widget.enabled ? _sendTextMessage : null,
                  ),
                ),
              ),
              
              const SizedBox(width: 8),
              
              // 发送按钮
              Container(
                decoration: BoxDecoration(
                  color: widget.enabled && widget.controller.text.isNotEmpty
                      ? Colors.blue[600]
                      : Colors.grey[400],
                  shape: BoxShape.circle,
                ),
                child: IconButton(
                  onPressed: widget.enabled && widget.controller.text.isNotEmpty
                      ? () => _sendTextMessage(widget.controller.text)
                      : null,
                  icon: const Icon(
                    Icons.send,
                    color: Colors.white,
                  ),
                  tooltip: '发送',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAttachmentOptions() {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildAttachmentOption(
            icon: Icons.photo,
            label: '图片',
            color: Colors.green,
            onTap: _pickImage,
          ),
          _buildAttachmentOption(
            icon: Icons.attach_file,
            label: '文件',
            color: Colors.blue,
            onTap: _pickFile,
          ),
          _buildAttachmentOption(
            icon: Icons.mic,
            label: '语音',
            color: Colors.orange,
            onTap: _recordAudio,
          ),
        ],
      ),
    );
  }

  Widget _buildAttachmentOption({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              shape: BoxShape.circle,
              border: Border.all(color: color.withOpacity(0.3)),
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[700],
            ),
          ),
        ],
      ),
    );
  }

  void _toggleAttachmentOptions() {
    setState(() {
      _showAttachmentOptions = !_showAttachmentOptions;
    });
  }

  void _sendTextMessage(String text) {
    if (text.trim().isEmpty) return;
    
    widget.onSend(text.trim());
    widget.controller.clear();
    _focusNode.requestFocus();
    
    // 隐藏附件选项
    if (_showAttachmentOptions) {
      setState(() {
        _showAttachmentOptions = false;
      });
    }
  }

  void _pickImage() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        // 这里应该先上传图片到服务器，然后发送图片URL
        // 暂时直接发送本地路径作为示例
        widget.onSend(image.path, messageType: 'image');
        
        setState(() {
          _showAttachmentOptions = false;
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('选择图片失败: $e')),
      );
    }
  }

  void _pickFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.any,
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        final file = File(result.files.single.path!);
        
        // 检查文件大小（限制为10MB）
        final fileSize = await file.length();
        if (fileSize > 10 * 1024 * 1024) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('文件大小不能超过10MB')),
          );
          return;
        }

        // 这里应该先上传文件到服务器，然后发送文件URL
        // 暂时直接发送本地路径作为示例
        widget.onSend(file.path, messageType: 'file');
        
        setState(() {
          _showAttachmentOptions = false;
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('选择文件失败: $e')),
      );
    }
  }

  void _recordAudio() {
    // 这里实现语音录制功能
    // 可以使用 flutter_sound 包来实现
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('语音功能开发中...')),
    );
    
    setState(() {
      _showAttachmentOptions = false;
    });
  }
}
