<?php

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/src/autoload.php';

use React\EventLoop\Loop;
use React\Socket\SocketServer;
use React\Http\HttpServer;
use React\Http\Message\Response;
use Psr\Http\Message\ServerRequestInterface;
use App\Utils\Logger;

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 配置
$host = '0.0.0.0';
$port = 8081;

// 存储连接的客户端
$clients = [];
$sessions = [];

try {
    Logger::info("Starting WebSocket server on {$host}:{$port}");

    $loop = Loop::get();

    // 创建Socket服务器
    $socket = new SocketServer("{$host}:{$port}", [], $loop);

    // 处理新连接
    $socket->on('connection', function ($connection) use (&$clients, &$sessions) {
        $clientId = uniqid();
        $clients[$clientId] = [
            'connection' => $connection,
            'authenticated' => false,
            'user_type' => null,
            'user_id' => null,
            'session_id' => null
        ];

        Logger::info("New connection: {$clientId}");

        // 发送连接确认
        $connection->write(json_encode([
            'type' => 'connection_established',
            'client_id' => $clientId,
            'timestamp' => time()
        ]) . "\n");

        // 处理接收到的数据
        $connection->on('data', function ($data) use ($clientId, &$clients, &$sessions) {
            try {
                $lines = explode("\n", trim($data));
                foreach ($lines as $line) {
                    if (empty($line)) continue;

                    $message = json_decode($line, true);
                    if (!$message || !isset($message['type'])) {
                        continue;
                    }

                    handleMessage($clientId, $message, $clients, $sessions);
                }
            } catch (Exception $e) {
                Logger::error("Error processing message", [
                    'client_id' => $clientId,
                    'error' => $e->getMessage(),
                    'data' => $data
                ]);
            }
        });

        // 处理连接关闭
        $connection->on('close', function () use ($clientId, &$clients, &$sessions) {
            Logger::info("Connection closed: {$clientId}");

            // 清理连接
            if (isset($clients[$clientId])) {
                $client = $clients[$clientId];

                // 从会话中移除
                if ($client['session_id'] && isset($sessions[$client['session_id']])) {
                    unset($sessions[$client['session_id']][$clientId]);

                    // 通知会话中的其他用户
                    broadcastToSession($client['session_id'], [
                        'type' => 'user_left',
                        'user_type' => $client['user_type'],
                        'user_id' => $client['user_id'],
                        'timestamp' => time()
                    ], $sessions, $clients, $clientId);
                }

                unset($clients[$clientId]);
            }
        });

        $connection->on('error', function ($error) use ($clientId) {
            Logger::error("Connection error", [
                'client_id' => $clientId,
                'error' => $error->getMessage()
            ]);
        });
    });

    Logger::info("WebSocket server started successfully");
    echo "WebSocket server started on {$host}:{$port}\n";
    echo "Press Ctrl+C to stop the server\n";

    // 启动事件循环
    $loop->run();

} catch (Exception $e) {
    Logger::error("Failed to start WebSocket server", [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);

    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}

// 处理消息的函数
function handleMessage($clientId, $message, &$clients, &$sessions) {
    if (!isset($clients[$clientId])) {
        return;
    }

    $client = &$clients[$clientId];
    $connection = $client['connection'];

    Logger::info("Received message", [
        'client_id' => $clientId,
        'type' => $message['type']
    ]);

    switch ($message['type']) {
        case 'auth':
            handleAuth($clientId, $message, $clients);
            break;
        case 'join_session':
            handleJoinSession($clientId, $message, $clients, $sessions);
            break;
        case 'send_message':
            handleSendMessage($clientId, $message, $clients, $sessions);
            break;
        case 'typing':
            handleTyping($clientId, $message, $clients, $sessions);
            break;
        default:
            sendError($connection, 'Unknown message type');
    }
}

// 处理认证
function handleAuth($clientId, $message, &$clients) {
    if (!isset($clients[$clientId])) return;

    $client = &$clients[$clientId];
    $connection = $client['connection'];

    if (!isset($message['user_type']) || !isset($message['user_id'])) {
        sendError($connection, 'Missing authentication data');
        return;
    }

    $client['authenticated'] = true;
    $client['user_type'] = $message['user_type'];
    $client['user_id'] = $message['user_id'];

    sendSuccess($connection, 'authenticated', [
        'user_type' => $message['user_type'],
        'user_id' => $message['user_id']
    ]);

    Logger::info("User authenticated", [
        'client_id' => $clientId,
        'user_type' => $message['user_type'],
        'user_id' => $message['user_id']
    ]);
}

// 处理加入会话
function handleJoinSession($clientId, $message, &$clients, &$sessions) {
    if (!isset($clients[$clientId])) return;

    $client = &$clients[$clientId];
    $connection = $client['connection'];

    if (!$client['authenticated']) {
        sendError($connection, 'Not authenticated');
        return;
    }

    $sessionId = $message['session_id'] ?? null;
    if (!$sessionId) {
        sendError($connection, 'Missing session_id');
        return;
    }

    $client['session_id'] = $sessionId;

    if (!isset($sessions[$sessionId])) {
        $sessions[$sessionId] = [];
    }

    $sessions[$sessionId][$clientId] = $client;

    sendSuccess($connection, 'joined_session', [
        'session_id' => $sessionId
    ]);

    // 通知会话中的其他用户
    broadcastToSession($sessionId, [
        'type' => 'user_joined',
        'user_type' => $client['user_type'],
        'user_id' => $client['user_id'],
        'timestamp' => time()
    ], $sessions, $clients, $clientId);

    Logger::info("User joined session", [
        'client_id' => $clientId,
        'session_id' => $sessionId
    ]);
}

// 处理发送消息
function handleSendMessage($clientId, $message, &$clients, &$sessions) {
    if (!isset($clients[$clientId])) return;

    $client = $clients[$clientId];
    $connection = $client['connection'];

    if (!$client['authenticated']) {
        sendError($connection, 'Not authenticated');
        return;
    }

    $sessionId = $message['session_id'] ?? null;
    $content = $message['content'] ?? null;

    if (!$sessionId || !$content) {
        sendError($connection, 'Missing required fields');
        return;
    }

    try {
        // 这里应该保存消息到数据库
        // 为了简化，我们直接广播消息

        $messageData = [
            'type' => 'new_message',
            'message' => [
                'id' => uniqid(),
                'session_id' => $sessionId,
                'sender_type' => $client['user_type'],
                'sender_id' => $client['user_id'],
                'content' => $content,
                'message_type' => $message['message_type'] ?? 'text',
                'created_at' => date('Y-m-d H:i:s'),
                'timestamp' => time()
            ]
        ];

        // 广播消息到会话中的所有用户
        broadcastToSession($sessionId, $messageData, $sessions, $clients);

        Logger::info("Message sent", [
            'session_id' => $sessionId,
            'sender_type' => $client['user_type'],
            'sender_id' => $client['user_id']
        ]);

    } catch (Exception $e) {
        Logger::error("Failed to send message", [
            'error' => $e->getMessage(),
            'session_id' => $sessionId
        ]);
        sendError($connection, 'Failed to send message');
    }
}

// 处理打字状态
function handleTyping($clientId, $message, &$clients, &$sessions) {
    if (!isset($clients[$clientId])) return;

    $client = $clients[$clientId];

    if (!$client['authenticated']) return;

    $sessionId = $message['session_id'] ?? null;
    $isTyping = $message['is_typing'] ?? false;

    if (!$sessionId) return;

    // 广播打字状态到会话中的其他用户
    broadcastToSession($sessionId, [
        'type' => 'typing_status',
        'user_type' => $client['user_type'],
        'user_id' => $client['user_id'],
        'is_typing' => $isTyping,
        'timestamp' => time()
    ], $sessions, $clients, $clientId);
}

// 向会话中的所有用户广播消息
function broadcastToSession($sessionId, $data, &$sessions, &$clients, $excludeClientId = null) {
    if (!isset($sessions[$sessionId])) {
        return;
    }

    $message = json_encode($data) . "\n";

    foreach ($sessions[$sessionId] as $clientId => $client) {
        if ($excludeClientId && $clientId === $excludeClientId) {
            continue;
        }

        if (isset($clients[$clientId])) {
            try {
                $clients[$clientId]['connection']->write($message);
            } catch (Exception $e) {
                Logger::error("Failed to send message to client", [
                    'client_id' => $clientId,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }
}

// 发送成功响应
function sendSuccess($connection, $type, $data = []) {
    $connection->write(json_encode([
        'type' => $type,
        'success' => true,
        'data' => $data,
        'timestamp' => time()
    ]) . "\n");
}

// 发送错误响应
function sendError($connection, $message) {
    $connection->write(json_encode([
        'type' => 'error',
        'success' => false,
        'error' => $message,
        'timestamp' => time()
    ]) . "\n");
}
