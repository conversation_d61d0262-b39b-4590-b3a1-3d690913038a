<?php

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/src/autoload.php';

use Ratchet\Server\IoServer;
use Ratchet\Http\HttpServer;
use Ratchet\WebSocket\WsServer;
use App\WebSocket\ChatWebSocketServer;
use App\Utils\Logger;

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 配置
$host = '0.0.0.0';
$port = 8081;

try {
    Logger::info("Starting WebSocket server on {$host}:{$port}");
    
    // 创建WebSocket服务器
    $chatServer = new ChatWebSocketServer();
    
    $server = IoServer::factory(
        new HttpServer(
            new WsServer($chatServer)
        ),
        $port,
        $host
    );

    Logger::info("WebSocket server started successfully");
    echo "WebSocket server started on {$host}:{$port}\n";
    echo "Press Ctrl+C to stop the server\n";
    
    // 启动服务器
    $server->run();
    
} catch (Exception $e) {
    Logger::error("Failed to start WebSocket server", [
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
    
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
