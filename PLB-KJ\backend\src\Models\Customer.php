<?php

namespace App\Models;

use App\Helpers\Logger;

class Customer extends BaseModel
{
    protected $table = 'customers'; // 对应 plb_kj_customers 表
    
    /**
     * 根据用户名查找客户
     *
     * @param string $username
     * @return array|null
     */
    public function findByUsername($username)
    {
        return $this->findBy('username', $username);
    }
    
    /**
     * 根据邮箱查找客户
     *
     * @param string $email
     * @return array|null
     */
    public function findByEmail($email)
    {
        return $this->findBy('email', $email);
    }
    
    /**
     * 验证客户密码
     *
     * @param string $password
     * @param string $hash
     * @return bool
     */
    public function verifyPassword($password, $hash)
    {
        return password_verify($password, $hash);
    }
    
    /**
     * 创建新客户
     *
     * @param array $data
     * @return int|false
     */
    public function createCustomer($data)
    {
        if (isset($data['password'])) {
            $data['password_hash'] = password_hash($data['password'], PASSWORD_DEFAULT);
            unset($data['password']);
        }
        
        return $this->create($data);
    }
    
    /**
     * 更新客户信息
     *
     * @param int $id
     * @param array $data
     * @return bool
     */
    public function updateCustomer($id, $data)
    {
        if (isset($data['password'])) {
            $data['password_hash'] = password_hash($data['password'], PASSWORD_DEFAULT);
            unset($data['password']);
        }
        
        return $this->update($id, $data);
    }
    
    /**
     * 更新客户在线状态
     *
     * @param int $id
     * @param bool $isOnline
     * @return bool
     */
    public function updateOnlineStatus($id, $isOnline)
    {
        return $this->update($id, [
            'is_online' => $isOnline ? 1 : 0,
            'last_login_at' => $isOnline ? date('Y-m-d H:i:s') : null
        ]);
    }
    
    /**
     * 获取在线客户列表
     *
     * @return array
     */
    public function getOnlineCustomers()
    {
        return $this->findWhere(['is_online = ?'], [1]);
    }
}
