# PLB-KJ 数据库设置指南

## 🎯 概述

这个指南将帮助您在MySQL 5.7环境中正确设置PLB-KJ跨境电商管理系统的数据库。

## 📋 前提条件

- MySQL 5.7 或更高版本
- MySQL客户端工具
- 足够的数据库权限（创建数据库、表、索引等）

## 🚀 快速安装

### 方法1：使用自动化脚本（推荐）

#### Windows系统：
```bash
cd PLB-KJ/backend
import_database.bat
```

#### Linux/Mac系统：
```bash
cd PLB-KJ/backend
chmod +x import_database.sh
./import_database.sh
```

### 方法2：手动导入

```bash
# 1. 创建数据库
mysql -u root -p -e "CREATE DATABASE IF NOT EXISTS plb_kj_ecommerce CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 2. 导入数据库结构和数据
mysql -u root -p plb_kj_ecommerce < plb_kj_database_mysql57.sql
```

## 📁 文件说明

| 文件名 | 描述 |
|--------|------|
| `plb_kj_database_mysql57.sql` | MySQL 5.7兼容的完整数据库脚本 |
| `import_database.bat` | Windows自动导入脚本 |
| `import_database.sh` | Linux/Mac自动导入脚本 |
| `DATABASE_SETUP_GUIDE.md` | 本设置指南 |

## 🗄️ 数据库结构

### 核心表

| 表名 | 描述 | 记录数 |
|------|------|--------|
| `plb_kj_admin_users` | 管理员用户表 | 3条示例记录 |
| `plb_kj_customers` | 客户表 | 3条示例记录 |
| `plb_kj_chat_sessions` | 聊天会话表 | 3条示例记录 |
| `plb_kj_chat_messages` | 聊天消息表 | 6条示例记录 |
| `plb_kj_chat_quick_replies` | 快捷回复模板表 | 9条模板 |
| `plb_kj_online_status` | 在线状态表 | 空表 |
| `plb_kj_notifications` | 通知表 | 空表 |
| `plb_kj_categories` | 商品分类表 | 9条分类记录 |
| `plb_kj_currencies` | 货币表 | 4种货币 |
| `plb_kj_system_settings` | 系统设置表 | 16项配置 |

### 视图

- `plb_kj_chat_session_details` - 聊天会话详情视图（包含客户和客服信息）

### 触发器

- `update_last_message_time` - 自动更新会话最后消息时间

## 🔧 默认配置

### 管理员账户

| 用户名 | 邮箱 | 角色 | 密码 |
|--------|------|------|------|
| admin | <EMAIL> | admin | password |
| manager | <EMAIL> | manager | password |
| service1 | <EMAIL> | customer_service | password |

⚠️ **重要**: 请在首次登录后立即修改默认密码！

### 客户账户

| 用户名 | 邮箱 | 姓名 | 密码 |
|--------|------|------|------|
| customer1 | <EMAIL> | 张三 | password |
| customer2 | <EMAIL> | 李四 | password |
| customer3 | <EMAIL> | John Smith | password |

### 系统设置

| 设置项 | 值 | 描述 |
|--------|----|----|
| site_name | PLB-KJ跨境电商管理系统 | 网站名称 |
| currency_default | USD | 默认货币 |
| chat_enabled | 1 | 启用聊天功能 |
| chat_max_sessions_per_agent | 5 | 每个客服最大并发会话数 |

## 🔍 验证安装

安装完成后，运行以下SQL验证：

```sql
-- 检查表是否创建成功
SHOW TABLES LIKE 'plb_kj_%';

-- 检查数据是否导入成功
SELECT 'Admin Users' as table_name, COUNT(*) as count FROM plb_kj_admin_users
UNION ALL
SELECT 'Customers' as table_name, COUNT(*) as count FROM plb_kj_customers
UNION ALL
SELECT 'Chat Sessions' as table_name, COUNT(*) as count FROM plb_kj_chat_sessions
UNION ALL
SELECT 'System Settings' as table_name, COUNT(*) as count FROM plb_kj_system_settings;

-- 检查聊天功能配置
SELECT setting_key, setting_value 
FROM plb_kj_system_settings 
WHERE category = 'chat';
```

预期结果：
- 应该显示10个以 `plb_kj_` 开头的表
- Admin Users: 3条记录
- Customers: 3条记录  
- Chat Sessions: 3条记录
- System Settings: 16条记录

## 🛠️ 故障排除

### 常见错误及解决方案

#### 1. 字符集错误
```
ERROR 1273 (HY000): Unknown collation: 'utf8mb4_unicode_ci'
```
**解决方案**: 确保使用MySQL 5.7+，或将字符集改为 `utf8_general_ci`

#### 2. 外键约束错误
```
ERROR 1215 (HY000): Cannot add foreign key constraint
```
**解决方案**: 检查引用的表是否存在，确保字段类型匹配

#### 3. 权限不足
```
ERROR 1044 (42000): Access denied for user
```
**解决方案**: 确保MySQL用户有CREATE、INSERT、ALTER权限

#### 4. 表已存在错误
```
ERROR 1050 (42S01): Table already exists
```
**解决方案**: 这是正常的，脚本使用了 `IF NOT EXISTS`，会跳过已存在的表

### 重置数据库

如果需要完全重置数据库：

```sql
-- 删除数据库（谨慎操作！）
DROP DATABASE IF EXISTS plb_kj_ecommerce;

-- 重新创建数据库
CREATE DATABASE plb_kj_ecommerce CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 重新导入
USE plb_kj_ecommerce;
SOURCE plb_kj_database_mysql57.sql;
```

## 📞 技术支持

如果遇到问题：

1. 检查MySQL版本：`SELECT VERSION();`
2. 检查字符集支持：`SHOW CHARACTER SET LIKE 'utf8mb4';`
3. 查看错误日志：检查MySQL错误日志文件
4. 验证权限：`SHOW GRANTS FOR CURRENT_USER();`

## 🔄 更新数据库

如果需要更新现有数据库结构，请：

1. 备份现有数据
2. 运行更新脚本
3. 验证数据完整性

---

**版本**: v1.0.0  
**兼容性**: MySQL 5.7+  
**最后更新**: 2024-01-01
