<?php

namespace App\Controllers;

use App\Helpers\Database;
use App\Helpers\JWTHelper;
use Exception;

class ChatController extends BaseController
{
    private $db;

    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * 获取聊天会话列表
     */
    public function getSessions()
    {
        try {
            $user = $this->getCurrentUser();
            $userType = $user['user_type'] ?? 'customer';
            $userId = $user['user_id'];

            // 构建查询条件
            if ($userType === 'admin') {
                // 管理员可以看到所有会话或分配给自己的会话
                $sql = "SELECT s.*, 
                               c.first_name as customer_first_name, 
                               c.last_name as customer_last_name,
                               c.email as customer_email,
                               c.avatar as customer_avatar,
                               c.is_online as customer_is_online,
                               a.first_name as admin_first_name,
                               a.last_name as admin_last_name,
                               a.email as admin_email,
                               a.avatar as admin_avatar,
                               a.is_online as admin_is_online,
                               (SELECT COUNT(*) FROM plb_kj_chat_messages m 
                                WHERE m.session_id = s.id AND m.is_read = 0 
                                AND m.sender_type = 'customer') as unread_count
                        FROM plb_kj_chat_sessions s
                        LEFT JOIN plb_kj_customers c ON s.customer_id = c.id
                        LEFT JOIN plb_kj_admin_users a ON s.admin_user_id = a.id
                        WHERE s.admin_user_id IS NULL OR s.admin_user_id = ?
                        ORDER BY s.last_message_at DESC";
                $stmt = $this->db->prepare($sql);
                $stmt->execute([$userId]);
            } else {
                // 客户只能看到自己的会话
                $sql = "SELECT s.*, 
                               c.first_name as customer_first_name, 
                               c.last_name as customer_last_name,
                               c.email as customer_email,
                               c.avatar as customer_avatar,
                               c.is_online as customer_is_online,
                               a.first_name as admin_first_name,
                               a.last_name as admin_last_name,
                               a.email as admin_email,
                               a.avatar as admin_avatar,
                               a.is_online as admin_is_online,
                               (SELECT COUNT(*) FROM plb_kj_chat_messages m 
                                WHERE m.session_id = s.id AND m.is_read = 0 
                                AND m.sender_type = 'admin') as unread_count
                        FROM plb_kj_chat_sessions s
                        LEFT JOIN plb_kj_customers c ON s.customer_id = c.id
                        LEFT JOIN plb_kj_admin_users a ON s.admin_user_id = a.id
                        WHERE s.customer_id = ?
                        ORDER BY s.last_message_at DESC";
                $stmt = $this->db->prepare($sql);
                $stmt->execute([$userId]);
            }

            $sessions = $stmt->fetchAll(\PDO::FETCH_ASSOC);

            // 格式化会话数据
            $formattedSessions = array_map(function($session) {
                return [
                    'id' => (int)$session['id'],
                    'session_id' => $session['session_id'],
                    'customer_id' => (int)$session['customer_id'],
                    'admin_user_id' => $session['admin_user_id'] ? (int)$session['admin_user_id'] : null,
                    'title' => $session['title'],
                    'status' => $session['status'],
                    'priority' => $session['priority'],
                    'category' => $session['category'],
                    'started_at' => $session['started_at'],
                    'ended_at' => $session['ended_at'],
                    'last_message_at' => $session['last_message_at'],
                    'customer_satisfaction_rating' => $session['customer_satisfaction_rating'] ? (int)$session['customer_satisfaction_rating'] : null,
                    'customer_feedback' => $session['customer_feedback'],
                    'unread_count' => (int)$session['unread_count'],
                    'customer' => [
                        'id' => (int)$session['customer_id'],
                        'first_name' => $session['customer_first_name'],
                        'last_name' => $session['customer_last_name'],
                        'email' => $session['customer_email'],
                        'avatar' => $session['customer_avatar'],
                        'is_online' => (bool)$session['customer_is_online']
                    ],
                    'admin' => $session['admin_user_id'] ? [
                        'id' => (int)$session['admin_user_id'],
                        'first_name' => $session['admin_first_name'],
                        'last_name' => $session['admin_last_name'],
                        'email' => $session['admin_email'],
                        'avatar' => $session['admin_avatar'],
                        'is_online' => (bool)$session['admin_is_online']
                    ] : null
                ];
            }, $sessions);

            $this->sendResponse([
                'sessions' => $formattedSessions
            ]);

        } catch (Exception $e) {
            $this->sendError('获取会话列表失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 创建新的聊天会话
     */
    public function createSession()
    {
        try {
            $user = $this->getCurrentUser();
            $userId = $user['user_id'];
            $userType = $user['user_type'] ?? 'customer';

            // 只有客户可以创建会话
            if ($userType !== 'customer') {
                $this->sendError('只有客户可以创建会话', 403);
                return;
            }

            $data = $this->getRequestData();
            $title = $data['title'] ?? '客服咨询';
            $category = $data['category'] ?? 'general';
            $priority = $data['priority'] ?? 'normal';

            // 检查是否已有活跃会话
            $sql = "SELECT id FROM plb_kj_chat_sessions 
                    WHERE customer_id = ? AND status IN ('waiting', 'active') 
                    ORDER BY created_at DESC LIMIT 1";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$userId]);
            $existingSession = $stmt->fetch();

            if ($existingSession) {
                $this->sendError('您已有活跃的会话，请先结束当前会话', 400);
                return;
            }

            // 生成会话ID
            $sessionId = $this->generateUUID();

            // 创建新会话
            $sql = "INSERT INTO plb_kj_chat_sessions 
                    (session_id, customer_id, title, category, priority, status, started_at, last_message_at) 
                    VALUES (?, ?, ?, ?, ?, 'waiting', NOW(), NOW())";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$sessionId, $userId, $title, $category, $priority]);

            $newSessionId = $this->db->lastInsertId();

            // 尝试自动分配客服
            $this->autoAssignAgent($newSessionId);

            // 获取创建的会话详情
            $sql = "SELECT s.*, 
                           c.first_name as customer_first_name, 
                           c.last_name as customer_last_name,
                           c.email as customer_email,
                           c.avatar as customer_avatar,
                           c.is_online as customer_is_online
                    FROM plb_kj_chat_sessions s
                    LEFT JOIN plb_kj_customers c ON s.customer_id = c.id
                    WHERE s.id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$newSessionId]);
            $session = $stmt->fetch(\PDO::FETCH_ASSOC);

            $this->sendResponse([
                'session' => [
                    'id' => (int)$session['id'],
                    'session_id' => $session['session_id'],
                    'customer_id' => (int)$session['customer_id'],
                    'admin_user_id' => $session['admin_user_id'] ? (int)$session['admin_user_id'] : null,
                    'title' => $session['title'],
                    'status' => $session['status'],
                    'priority' => $session['priority'],
                    'category' => $session['category'],
                    'started_at' => $session['started_at'],
                    'last_message_at' => $session['last_message_at'],
                    'customer' => [
                        'id' => (int)$session['customer_id'],
                        'first_name' => $session['customer_first_name'],
                        'last_name' => $session['customer_last_name'],
                        'email' => $session['customer_email'],
                        'avatar' => $session['customer_avatar'],
                        'is_online' => (bool)$session['customer_is_online']
                    ]
                ]
            ]);

        } catch (Exception $e) {
            $this->sendError('创建会话失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取会话消息
     */
    public function getSessionMessages($sessionId)
    {
        try {
            $user = $this->getCurrentUser();
            $userId = $user['user_id'];
            $userType = $user['user_type'] ?? 'customer';

            // 验证会话权限
            if (!$this->hasSessionAccess($sessionId, $userId, $userType)) {
                $this->sendError('无权访问此会话', 403);
                return;
            }

            $limit = $_GET['limit'] ?? 50;
            $offset = $_GET['offset'] ?? 0;

            $sql = "SELECT m.*, 
                           CASE 
                               WHEN m.sender_type = 'customer' THEN c.first_name
                               WHEN m.sender_type = 'admin' THEN a.first_name
                           END as sender_first_name,
                           CASE 
                               WHEN m.sender_type = 'customer' THEN c.last_name
                               WHEN m.sender_type = 'admin' THEN a.last_name
                           END as sender_last_name,
                           CASE 
                               WHEN m.sender_type = 'customer' THEN c.avatar
                               WHEN m.sender_type = 'admin' THEN a.avatar
                           END as sender_avatar
                    FROM plb_kj_chat_messages m
                    LEFT JOIN plb_kj_customers c ON m.sender_type = 'customer' AND m.sender_id = c.id
                    LEFT JOIN plb_kj_admin_users a ON m.sender_type = 'admin' AND m.sender_id = a.id
                    WHERE m.session_id = ?
                    ORDER BY m.created_at ASC
                    LIMIT ? OFFSET ?";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$sessionId, $limit, $offset]);
            $messages = $stmt->fetchAll(\PDO::FETCH_ASSOC);

            // 格式化消息数据
            $formattedMessages = array_map(function($message) {
                return [
                    'id' => (int)$message['id'],
                    'session_id' => (int)$message['session_id'],
                    'sender_type' => $message['sender_type'],
                    'sender_id' => (int)$message['sender_id'],
                    'sender_name' => trim($message['sender_first_name'] . ' ' . $message['sender_last_name']),
                    'sender_avatar' => $message['sender_avatar'],
                    'message_type' => $message['message_type'],
                    'content' => $message['content'],
                    'reply_to_message_id' => $message['reply_to_message_id'] ? (int)$message['reply_to_message_id'] : null,
                    'is_read' => (bool)$message['is_read'],
                    'created_at' => $message['created_at'],
                    'read_at' => $message['read_at']
                ];
            }, $messages);

            $this->sendResponse([
                'messages' => $formattedMessages
            ]);

        } catch (Exception $e) {
            $this->sendError('获取消息失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 发送消息
     */
    public function sendMessage($sessionId)
    {
        try {
            $user = $this->getCurrentUser();
            $userId = $user['user_id'];
            $userType = $user['user_type'] ?? 'customer';

            // 验证会话权限
            if (!$this->hasSessionAccess($sessionId, $userId, $userType)) {
                $this->sendError('无权访问此会话', 403);
                return;
            }

            $data = $this->getRequestData();
            $content = $data['content'] ?? '';
            $messageType = $data['message_type'] ?? 'text';
            $replyToMessageId = $data['reply_to_message_id'] ?? null;

            if (empty($content)) {
                $this->sendError('消息内容不能为空', 400);
                return;
            }

            // 插入消息
            $sql = "INSERT INTO plb_kj_chat_messages 
                    (session_id, sender_type, sender_id, message_type, content, reply_to_message_id, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, NOW())";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$sessionId, $userType, $userId, $messageType, $content, $replyToMessageId]);

            $messageId = $this->db->lastInsertId();

            // 更新会话最后消息时间
            $sql = "UPDATE plb_kj_chat_sessions SET last_message_at = NOW() WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$sessionId]);

            // 获取发送者信息
            $senderInfo = $this->getSenderInfo($userId, $userType);

            $this->sendResponse([
                'message' => [
                    'id' => (int)$messageId,
                    'session_id' => (int)$sessionId,
                    'sender_type' => $userType,
                    'sender_id' => (int)$userId,
                    'sender_name' => $senderInfo['name'],
                    'sender_avatar' => $senderInfo['avatar'],
                    'message_type' => $messageType,
                    'content' => $content,
                    'reply_to_message_id' => $replyToMessageId ? (int)$replyToMessageId : null,
                    'is_read' => false,
                    'created_at' => date('Y-m-d H:i:s')
                ]
            ]);

        } catch (Exception $e) {
            $this->sendError('发送消息失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 接受会话（管理员）
     */
    public function acceptSession($sessionId)
    {
        try {
            $user = $this->getCurrentUser();
            $userId = $user['user_id'];
            $userType = $user['user_type'] ?? 'customer';

            if ($userType !== 'admin') {
                $this->sendError('只有管理员可以接受会话', 403);
                return;
            }

            // 更新会话状态
            $sql = "UPDATE plb_kj_chat_sessions 
                    SET admin_user_id = ?, status = 'active', updated_at = NOW() 
                    WHERE id = ? AND status = 'waiting'";
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([$userId, $sessionId]);

            if ($stmt->rowCount() === 0) {
                $this->sendError('会话不存在或已被其他客服接受', 400);
                return;
            }

            $this->sendResponse([
                'message' => '会话已接受',
                'session_id' => (int)$sessionId,
                'admin_user_id' => (int)$userId
            ]);

        } catch (Exception $e) {
            $this->sendError('接受会话失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 关闭会话
     */
    public function closeSession($sessionId)
    {
        try {
            $user = $this->getCurrentUser();
            $userId = $user['user_id'];
            $userType = $user['user_type'] ?? 'customer';

            // 验证会话权限
            if (!$this->hasSessionAccess($sessionId, $userId, $userType)) {
                $this->sendError('无权访问此会话', 403);
                return;
            }

            $data = $this->getRequestData();
            $rating = $data['rating'] ?? null;
            $feedback = $data['feedback'] ?? null;

            // 更新会话状态
            $sql = "UPDATE plb_kj_chat_sessions 
                    SET status = 'closed', ended_at = NOW(), 
                        customer_satisfaction_rating = ?, customer_feedback = ?, updated_at = NOW() 
                    WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$rating, $feedback, $sessionId]);

            $this->sendResponse([
                'message' => '会话已关闭',
                'session_id' => (int)$sessionId,
                'rating' => $rating ? (int)$rating : null,
                'feedback' => $feedback
            ]);

        } catch (Exception $e) {
            $this->sendError('关闭会话失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 标记消息已读
     */
    public function markMessagesRead($sessionId)
    {
        try {
            $user = $this->getCurrentUser();
            $userId = $user['user_id'];
            $userType = $user['user_type'] ?? 'customer';

            // 验证会话权限
            if (!$this->hasSessionAccess($sessionId, $userId, $userType)) {
                $this->sendError('无权访问此会话', 403);
                return;
            }

            $data = $this->getRequestData();
            $messageIds = $data['message_ids'] ?? [];

            if (!empty($messageIds)) {
                $placeholders = str_repeat('?,', count($messageIds) - 1) . '?';
                $sql = "UPDATE plb_kj_chat_messages 
                        SET is_read = 1, read_at = NOW() 
                        WHERE id IN ($placeholders) AND session_id = ?";
                $params = array_merge($messageIds, [$sessionId]);
                $stmt = $this->db->prepare($sql);
                $stmt->execute($params);
            }

            $this->sendResponse([
                'message' => '消息已标记为已读',
                'session_id' => (int)$sessionId,
                'message_ids' => array_map('intval', $messageIds)
            ]);

        } catch (Exception $e) {
            $this->sendError('标记消息已读失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取聊天统计
     */
    public function getChatStats()
    {
        try {
            $user = $this->getCurrentUser();
            $userType = $user['user_type'] ?? 'customer';

            if ($userType !== 'admin') {
                $this->sendError('只有管理员可以查看统计', 403);
                return;
            }

            // 获取今日统计
            $sql = "SELECT 
                        COUNT(*) as total_sessions,
                        SUM(CASE WHEN status = 'waiting' THEN 1 ELSE 0 END) as waiting_sessions,
                        SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_sessions,
                        SUM(CASE WHEN status = 'closed' THEN 1 ELSE 0 END) as closed_sessions,
                        AVG(customer_satisfaction_rating) as avg_rating,
                        (SELECT COUNT(*) FROM plb_kj_chat_messages WHERE DATE(created_at) = CURDATE()) as total_messages
                    FROM plb_kj_chat_sessions 
                    WHERE DATE(started_at) = CURDATE()";
            
            $stmt = $this->db->query($sql);
            $stats = $stmt->fetch(\PDO::FETCH_ASSOC);

            $this->sendResponse([
                'stats' => [
                    'total_sessions' => (int)$stats['total_sessions'],
                    'waiting_sessions' => (int)$stats['waiting_sessions'],
                    'active_sessions' => (int)$stats['active_sessions'],
                    'closed_sessions' => (int)$stats['closed_sessions'],
                    'total_messages' => (int)$stats['total_messages'],
                    'average_rating' => $stats['avg_rating'] ? round($stats['avg_rating'], 2) : null
                ]
            ]);

        } catch (Exception $e) {
            $this->sendError('获取统计失败: ' . $e->getMessage(), 500);
        }
    }

    // 私有辅助方法

    private function hasSessionAccess($sessionId, $userId, $userType)
    {
        if ($userType === 'admin') {
            $sql = "SELECT id FROM plb_kj_chat_sessions 
                    WHERE id = ? AND (admin_user_id IS NULL OR admin_user_id = ?)";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$sessionId, $userId]);
        } else {
            $sql = "SELECT id FROM plb_kj_chat_sessions WHERE id = ? AND customer_id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$sessionId, $userId]);
        }
        
        return $stmt->fetch() !== false;
    }

    private function autoAssignAgent($sessionId)
    {
        // 查找在线且会话数最少的客服
        $sql = "SELECT au.id, COUNT(cs.id) as active_sessions
                FROM plb_kj_admin_users au
                LEFT JOIN plb_kj_chat_sessions cs ON au.id = cs.admin_user_id AND cs.status = 'active'
                WHERE au.role = 'customer_service' 
                  AND au.status = 1 
                  AND au.is_online = 1
                GROUP BY au.id
                HAVING active_sessions < 10
                ORDER BY active_sessions ASC, RAND()
                LIMIT 1";
        
        $stmt = $this->db->query($sql);
        $agent = $stmt->fetch(\PDO::FETCH_ASSOC);

        if ($agent) {
            $sql = "UPDATE plb_kj_chat_sessions 
                    SET admin_user_id = ?, status = 'active', updated_at = NOW() 
                    WHERE id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$agent['id'], $sessionId]);
        }
    }

    private function getSenderInfo($userId, $userType)
    {
        if ($userType === 'admin') {
            $sql = "SELECT first_name, last_name, avatar FROM plb_kj_admin_users WHERE id = ?";
        } else {
            $sql = "SELECT first_name, last_name, avatar FROM plb_kj_customers WHERE id = ?";
        }
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId]);
        $user = $stmt->fetch(\PDO::FETCH_ASSOC);

        return [
            'name' => trim($user['first_name'] . ' ' . $user['last_name']),
            'avatar' => $user['avatar']
        ];
    }

    private function generateUUID()
    {
        return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }
}
