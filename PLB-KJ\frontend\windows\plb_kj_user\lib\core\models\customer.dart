import 'package:json_annotation/json_annotation.dart';

part 'customer.g.dart';

/// 客户模型
@JsonSerializable()
class Customer {
  final int id;
  final String? username;
  final String email;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'first_name')
  final String firstName;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'last_name')
  final String lastName;
  final String? phone;
  final String? country;
  final String? city;
  final String? address;
  @<PERSON>son<PERSON>ey(name: 'postal_code')
  final String? postalCode;
  final String? avatar;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'language_preference')
  final String languagePreference;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'currency_preference')
  final String currencyPreference;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_online')
  final bool isOnline;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'last_login_at')
  final DateTime? lastLoginAt;
  @<PERSON>son<PERSON>ey(name: 'email_verified')
  final bool emailVerified;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'email_verified_at')
  final DateTime? emailVerifiedAt;
  final bool status;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final DateTime createdAt;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at')
  final DateTime updatedAt;

  const Customer({
    required this.id,
    this.username,
    required this.email,
    required this.firstName,
    required this.lastName,
    this.phone,
    this.country,
    this.city,
    this.address,
    this.postalCode,
    this.avatar,
    required this.languagePreference,
    required this.currencyPreference,
    required this.isOnline,
    this.lastLoginAt,
    required this.emailVerified,
    this.emailVerifiedAt,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Customer.fromJson(Map<String, dynamic> json) =>
      _$CustomerFromJson(json);

  Map<String, dynamic> toJson() => _$CustomerToJson(this);

  /// 获取完整姓名
  String get fullName => '$firstName $lastName';

  /// 获取显示名称
  String get displayName => username?.isNotEmpty == true ? username! : fullName;

  /// 获取状态显示名称
  String get statusDisplayName => status ? '正常' : '禁用';

  /// 获取在线状态显示名称
  String get onlineStatusDisplayName => isOnline ? '在线' : '离线';

  /// 获取完整地址
  String get fullAddress {
    final parts = <String>[];
    if (country?.isNotEmpty == true) parts.add(country!);
    if (city?.isNotEmpty == true) parts.add(city!);
    if (address?.isNotEmpty == true) parts.add(address!);
    if (postalCode?.isNotEmpty == true) parts.add(postalCode!);
    return parts.join(', ');
  }

  /// 复制并更新部分字段
  Customer copyWith({
    int? id,
    String? username,
    String? email,
    String? firstName,
    String? lastName,
    String? phone,
    String? country,
    String? city,
    String? address,
    String? postalCode,
    String? avatar,
    String? languagePreference,
    String? currencyPreference,
    bool? isOnline,
    DateTime? lastLoginAt,
    bool? emailVerified,
    DateTime? emailVerifiedAt,
    bool? status,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Customer(
      id: id ?? this.id,
      username: username ?? this.username,
      email: email ?? this.email,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      phone: phone ?? this.phone,
      country: country ?? this.country,
      city: city ?? this.city,
      address: address ?? this.address,
      postalCode: postalCode ?? this.postalCode,
      avatar: avatar ?? this.avatar,
      languagePreference: languagePreference ?? this.languagePreference,
      currencyPreference: currencyPreference ?? this.currencyPreference,
      isOnline: isOnline ?? this.isOnline,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      emailVerified: emailVerified ?? this.emailVerified,
      emailVerifiedAt: emailVerifiedAt ?? this.emailVerifiedAt,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// 客户登录请求模型
@JsonSerializable()
class CustomerLoginRequest {
  final String email;
  final String password;

  const CustomerLoginRequest({
    required this.email,
    required this.password,
  });

  factory CustomerLoginRequest.fromJson(Map<String, dynamic> json) =>
      _$CustomerLoginRequestFromJson(json);

  Map<String, dynamic> toJson() => _$CustomerLoginRequestToJson(this);
}

/// 客户注册请求模型
@JsonSerializable()
class CustomerRegisterRequest {
  final String? username;
  final String email;
  final String password;
  @JsonKey(name: 'first_name')
  final String firstName;
  @JsonKey(name: 'last_name')
  final String lastName;
  final String? phone;
  final String? country;
  final String? city;
  final String? address;
  @JsonKey(name: 'postal_code')
  final String? postalCode;
  @JsonKey(name: 'language_preference')
  final String? languagePreference;
  @JsonKey(name: 'currency_preference')
  final String? currencyPreference;

  const CustomerRegisterRequest({
    this.username,
    required this.email,
    required this.password,
    required this.firstName,
    required this.lastName,
    this.phone,
    this.country,
    this.city,
    this.address,
    this.postalCode,
    this.languagePreference,
    this.currencyPreference,
  });

  factory CustomerRegisterRequest.fromJson(Map<String, dynamic> json) =>
      _$CustomerRegisterRequestFromJson(json);

  Map<String, dynamic> toJson() => _$CustomerRegisterRequestToJson(this);
}

/// 客户登录响应模型
@JsonSerializable()
class CustomerLoginResponse {
  final String token;
  final Customer customer;

  const CustomerLoginResponse({
    required this.token,
    required this.customer,
  });

  factory CustomerLoginResponse.fromJson(Map<String, dynamic> json) =>
      _$CustomerLoginResponseFromJson(json);

  Map<String, dynamic> toJson() => _$CustomerLoginResponseToJson(this);
}
