// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'customer.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Customer _$CustomerFromJson(Map<String, dynamic> json) => Customer(
  id: (json['id'] as num).toInt(),
  username: json['username'] as String?,
  email: json['email'] as String,
  firstName: json['first_name'] as String,
  lastName: json['last_name'] as String,
  phone: json['phone'] as String?,
  country: json['country'] as String?,
  city: json['city'] as String?,
  address: json['address'] as String?,
  postalCode: json['postal_code'] as String?,
  avatar: json['avatar'] as String?,
  languagePreference: json['language_preference'] as String,
  currencyPreference: json['currency_preference'] as String,
  isOnline: json['is_online'] as bool,
  lastLoginAt: json['last_login_at'] == null
      ? null
      : DateTime.parse(json['last_login_at'] as String),
  emailVerified: json['email_verified'] as bool,
  emailVerifiedAt: json['email_verified_at'] == null
      ? null
      : DateTime.parse(json['email_verified_at'] as String),
  status: json['status'] as bool,
  createdAt: DateTime.parse(json['created_at'] as String),
  updatedAt: DateTime.parse(json['updated_at'] as String),
);

Map<String, dynamic> _$CustomerToJson(Customer instance) => <String, dynamic>{
  'id': instance.id,
  'username': instance.username,
  'email': instance.email,
  'first_name': instance.firstName,
  'last_name': instance.lastName,
  'phone': instance.phone,
  'country': instance.country,
  'city': instance.city,
  'address': instance.address,
  'postal_code': instance.postalCode,
  'avatar': instance.avatar,
  'language_preference': instance.languagePreference,
  'currency_preference': instance.currencyPreference,
  'is_online': instance.isOnline,
  'last_login_at': instance.lastLoginAt?.toIso8601String(),
  'email_verified': instance.emailVerified,
  'email_verified_at': instance.emailVerifiedAt?.toIso8601String(),
  'status': instance.status,
  'created_at': instance.createdAt.toIso8601String(),
  'updated_at': instance.updatedAt.toIso8601String(),
};

CustomerLoginRequest _$CustomerLoginRequestFromJson(
  Map<String, dynamic> json,
) => CustomerLoginRequest(
  email: json['email'] as String,
  password: json['password'] as String,
);

Map<String, dynamic> _$CustomerLoginRequestToJson(
  CustomerLoginRequest instance,
) => <String, dynamic>{'email': instance.email, 'password': instance.password};

CustomerRegisterRequest _$CustomerRegisterRequestFromJson(
  Map<String, dynamic> json,
) => CustomerRegisterRequest(
  username: json['username'] as String?,
  email: json['email'] as String,
  password: json['password'] as String,
  firstName: json['first_name'] as String,
  lastName: json['last_name'] as String,
  phone: json['phone'] as String?,
  country: json['country'] as String?,
  city: json['city'] as String?,
  address: json['address'] as String?,
  postalCode: json['postal_code'] as String?,
  languagePreference: json['language_preference'] as String?,
  currencyPreference: json['currency_preference'] as String?,
);

Map<String, dynamic> _$CustomerRegisterRequestToJson(
  CustomerRegisterRequest instance,
) => <String, dynamic>{
  'username': instance.username,
  'email': instance.email,
  'password': instance.password,
  'first_name': instance.firstName,
  'last_name': instance.lastName,
  'phone': instance.phone,
  'country': instance.country,
  'city': instance.city,
  'address': instance.address,
  'postal_code': instance.postalCode,
  'language_preference': instance.languagePreference,
  'currency_preference': instance.currencyPreference,
};

CustomerLoginResponse _$CustomerLoginResponseFromJson(
  Map<String, dynamic> json,
) => CustomerLoginResponse(
  token: json['token'] as String,
  customer: Customer.fromJson(json['customer'] as Map<String, dynamic>),
);

Map<String, dynamic> _$CustomerLoginResponseToJson(
  CustomerLoginResponse instance,
) => <String, dynamic>{'token': instance.token, 'customer': instance.customer};
