import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/chat_models.dart';

class SessionListItem extends StatelessWidget {
  final ChatSession session;
  final VoidCallback onTap;
  final VoidCallback? onAccept;
  final VoidCallback? onClose;

  const SessionListItem({
    Key? key,
    required this.session,
    required this.onTap,
    this.onAccept,
    this.onClose,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 头部信息
              Row(
                children: [
                  // 客户头像
                  CircleAvatar(
                    radius: 20,
                    backgroundColor: Colors.blue[100],
                    backgroundImage: session.customer?.avatar != null
                        ? NetworkImage(session.customer!.avatar!)
                        : null,
                    child: session.customer?.avatar == null
                        ? Text(
                            session.customer?.fullName.substring(0, 1).toUpperCase() ?? 'C',
                            style: TextStyle(
                              color: Colors.blue[800],
                              fontWeight: FontWeight.bold,
                            ),
                          )
                        : null,
                  ),
                  const SizedBox(width: 12),
                  
                  // 客户信息
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                session.customer?.fullName ?? '客户 ${session.customerId}',
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                            ),
                            // 在线状态
                            if (session.customer?.isOnline == true)
                              Container(
                                width: 8,
                                height: 8,
                                decoration: const BoxDecoration(
                                  color: Colors.green,
                                  shape: BoxShape.circle,
                                ),
                              ),
                          ],
                        ),
                        const SizedBox(height: 2),
                        Text(
                          session.title,
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // 状态标签
                  _buildStatusChip(),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // 会话详情
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.schedule,
                              size: 14,
                              color: Colors.grey[600],
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '开始时间: ${DateFormat('MM-dd HH:mm').format(session.startedAt)}',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Icon(
                              Icons.message,
                              size: 14,
                              color: Colors.grey[600],
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '最后消息: ${DateFormat('MM-dd HH:mm').format(session.lastMessageAt)}',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  
                  // 优先级标签
                  _buildPriorityChip(),
                ],
              ),
              
              // 未读消息数量
              if (session.unreadCount > 0)
                Container(
                  margin: const EdgeInsets.only(top: 8),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          '${session.unreadCount} 条未读',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              
              // 操作按钮
              if (onAccept != null || onClose != null)
                Container(
                  margin: const EdgeInsets.only(top: 12),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      if (onAccept != null)
                        ElevatedButton.icon(
                          onPressed: onAccept,
                          icon: const Icon(Icons.check, size: 16),
                          label: const Text('接受'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                            minimumSize: Size.zero,
                          ),
                        ),
                      if (onAccept != null && onClose != null)
                        const SizedBox(width: 8),
                      if (onClose != null)
                        ElevatedButton.icon(
                          onPressed: onClose,
                          icon: const Icon(Icons.close, size: 16),
                          label: const Text('结束'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                            minimumSize: Size.zero,
                          ),
                        ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip() {
    Color color;
    String text;
    IconData icon;

    switch (session.status) {
      case 'waiting':
        color = Colors.orange;
        text = '等待中';
        icon = Icons.schedule;
        break;
      case 'active':
        color = Colors.green;
        text = '进行中';
        icon = Icons.chat;
        break;
      case 'closed':
        color = Colors.grey;
        text = '已结束';
        icon = Icons.check_circle;
        break;
      default:
        color = Colors.grey;
        text = '未知';
        icon = Icons.help;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: color),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriorityChip() {
    Color color;
    String text;

    switch (session.priority) {
      case 'low':
        color = Colors.blue;
        text = '低';
        break;
      case 'normal':
        color = Colors.green;
        text = '普通';
        break;
      case 'high':
        color = Colors.orange;
        text = '高';
        break;
      case 'urgent':
        color = Colors.red;
        text = '紧急';
        break;
      default:
        color = Colors.grey;
        text = '普通';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 10,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}
