#!/bin/bash

echo "========================================"
echo "PLB-KJ 数据库导入脚本 (MySQL 5.7 兼容版)"
echo "========================================"
echo

# 获取数据库连接信息
read -p "请输入MySQL主机地址 (默认: localhost): " DB_HOST
DB_HOST=${DB_HOST:-localhost}

read -p "请输入MySQL端口 (默认: 3306): " DB_PORT
DB_PORT=${DB_PORT:-3306}

read -p "请输入MySQL用户名 (默认: root): " DB_USER
DB_USER=${DB_USER:-root}

read -s -p "请输入MySQL密码: " DB_PASS
echo

read -p "请输入数据库名称 (默认: plb_kj_ecommerce): " DB_NAME
DB_NAME=${DB_NAME:-plb_kj_ecommerce}

echo
echo "正在连接到MySQL服务器..."
echo "主机: $DB_HOST:$DB_PORT"
echo "用户: $DB_USER"
echo "数据库: $DB_NAME"
echo

# 检查MySQL是否可用
if ! command -v mysql &> /dev/null; then
    echo "错误: 未找到mysql命令，请确保MySQL客户端已安装"
    exit 1
fi

# 检查数据库是否存在，如果不存在则创建
echo "检查并创建数据库..."
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" -e "CREATE DATABASE IF NOT EXISTS $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" 2>/dev/null

if [ $? -ne 0 ]; then
    echo "错误: 无法连接到MySQL服务器或创建数据库"
    echo "请检查连接信息是否正确"
    exit 1
fi

echo "数据库创建成功！"
echo

# 检查SQL文件是否存在
if [ ! -f "plb_kj_database_mysql57.sql" ]; then
    echo "错误: 未找到 plb_kj_database_mysql57.sql 文件"
    echo "请确保在正确的目录下运行此脚本"
    exit 1
fi

echo "正在导入数据库结构和数据..."
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" < plb_kj_database_mysql57.sql

if [ $? -ne 0 ]; then
    echo "错误: 数据库导入失败"
    echo "请检查SQL文件是否正确或查看错误信息"
    exit 1
fi

echo
echo "========================================"
echo "数据库导入完成！"
echo "========================================"
echo
echo "默认管理员账户:"
echo "用户名: admin"
echo "密码: password (请登录后立即修改)"
echo "邮箱: <EMAIL>"
echo
echo "数据库连接信息:"
echo "主机: $DB_HOST:$DB_PORT"
echo "数据库: $DB_NAME"
echo
echo "请更新后端配置文件中的数据库连接信息"
echo

# 显示表统计信息
echo "数据库表统计:"
mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" -e "
SELECT 'Admin Users' as table_name, COUNT(*) as count FROM plb_kj_admin_users
UNION ALL
SELECT 'Customers' as table_name, COUNT(*) as count FROM plb_kj_customers
UNION ALL
SELECT 'Chat Sessions' as table_name, COUNT(*) as count FROM plb_kj_chat_sessions
UNION ALL
SELECT 'Chat Messages' as table_name, COUNT(*) as count FROM plb_kj_chat_messages
UNION ALL
SELECT 'System Settings' as table_name, COUNT(*) as count FROM plb_kj_system_settings;
" 2>/dev/null

echo
echo "导入完成！您现在可以启动PLB-KJ应用程序了。"
