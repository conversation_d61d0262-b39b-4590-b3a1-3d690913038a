<?php

namespace App\Controllers;

use App\Models\Customer;
use App\Exceptions\AuthException;
use App\Helpers\Logger;
use App\Services\CaptchaService;

class CustomerController extends BaseController
{
    private $customerModel;
    private $captchaService;
    
    public function __construct()
    {
        parent::__construct();
        $this->customerModel = new Customer();
        $this->captchaService = CaptchaService::getInstance();
    }
    
    /**
     * 客户登录
     *
     * @return array
     */
    public function login()
    {
        try {
            $input = $this->getJsonInput();
            
            // 验证必需字段
            if (empty($input['email']) || empty($input['password'])) {
                return $this->error('邮箱和密码不能为空', 400);
            }
            
            // 查找客户
            $customer = $this->customerModel->findByEmail($input['email']);
            if (!$customer) {
                return $this->error('客户不存在', 404);
            }
            
            // 验证密码
            if (!$this->customerModel->verifyPassword($input['password'], $customer['password_hash'])) {
                return $this->error('密码错误', 401);
            }
            
            // 检查客户状态
            if ($customer['status'] != 1) {
                return $this->error('账户已被禁用', 403);
            }
            
            // 更新在线状态和最后登录时间
            $this->customerModel->updateOnlineStatus($customer['id'], true);
            
            // 生成JWT令牌
            $payload = [
                'user_id' => $customer['id'],
                'user_type' => 'customer',
                'email' => $customer['email'],
                'exp' => time() + (24 * 60 * 60) // 24小时过期
            ];
            
            $token = $this->generateJWT($payload);
            
            // 记录日志
            Logger::info("客户登录成功", [
                'customer_id' => $customer['id'],
                'email' => $customer['email'],
                'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
            ]);
            
            return $this->success([
                'token' => $token,
                'customer' => [
                    'id' => $customer['id'],
                    'username' => $customer['username'],
                    'email' => $customer['email'],
                    'first_name' => $customer['first_name'],
                    'last_name' => $customer['last_name'],
                    'avatar' => $customer['avatar'],
                    'language_preference' => $customer['language_preference'],
                    'currency_preference' => $customer['currency_preference']
                ]
            ], '登录成功');
            
        } catch (\Exception $e) {
            Logger::error("客户登录失败", [
                'error' => $e->getMessage(),
                'email' => $input['email'] ?? 'unknown'
            ]);
            return $this->error('登录失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 客户注册
     *
     * @return array
     */
    public function register()
    {
        try {
            $input = $this->getJsonInput();
            
            // 验证必需字段
            $required = ['email', 'password', 'first_name', 'last_name'];
            foreach ($required as $field) {
                if (empty($input[$field])) {
                    return $this->error("字段 {$field} 不能为空", 400);
                }
            }
            
            // 检查邮箱是否已存在
            if ($this->customerModel->findByEmail($input['email'])) {
                return $this->error('邮箱已被注册', 409);
            }
            
            // 检查用户名是否已存在（如果提供了用户名）
            if (!empty($input['username']) && $this->customerModel->findByUsername($input['username'])) {
                return $this->error('用户名已被使用', 409);
            }
            
            // 创建客户
            $customerData = [
                'username' => $input['username'] ?? null,
                'email' => $input['email'],
                'password' => $input['password'],
                'first_name' => $input['first_name'],
                'last_name' => $input['last_name'],
                'phone' => $input['phone'] ?? null,
                'country' => $input['country'] ?? null,
                'city' => $input['city'] ?? null,
                'address' => $input['address'] ?? null,
                'postal_code' => $input['postal_code'] ?? null,
                'language_preference' => $input['language_preference'] ?? 'zh-CN',
                'currency_preference' => $input['currency_preference'] ?? 'USD'
            ];
            
            $customerId = $this->customerModel->createCustomer($customerData);
            
            if ($customerId) {
                Logger::info("客户注册成功", [
                    'customer_id' => $customerId,
                    'email' => $input['email']
                ]);
                
                return $this->success([
                    'customer_id' => $customerId
                ], '注册成功');
            } else {
                return $this->error('注册失败', 500);
            }
            
        } catch (\Exception $e) {
            Logger::error("客户注册失败", [
                'error' => $e->getMessage(),
                'email' => $input['email'] ?? 'unknown'
            ]);
            return $this->error('注册失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 获取客户资料
     *
     * @return array
     */
    public function getProfile()
    {
        try {
            $customerId = $this->getCurrentUserId();
            $customer = $this->customerModel->findById($customerId);
            
            if (!$customer) {
                return $this->error('客户不存在', 404);
            }
            
            // 移除敏感信息
            unset($customer['password_hash']);
            
            return $this->success($customer, '获取资料成功');
            
        } catch (\Exception $e) {
            return $this->error('获取资料失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 更新客户资料
     *
     * @return array
     */
    public function updateProfile()
    {
        try {
            $customerId = $this->getCurrentUserId();
            $input = $this->getJsonInput();
            
            // 允许更新的字段
            $allowedFields = [
                'username', 'first_name', 'last_name', 'phone', 
                'country', 'city', 'address', 'postal_code',
                'language_preference', 'currency_preference', 'avatar'
            ];
            
            $updateData = [];
            foreach ($allowedFields as $field) {
                if (isset($input[$field])) {
                    $updateData[$field] = $input[$field];
                }
            }
            
            if (empty($updateData)) {
                return $this->error('没有要更新的数据', 400);
            }
            
            // 如果更新用户名，检查是否已存在
            if (isset($updateData['username'])) {
                $existing = $this->customerModel->findByUsername($updateData['username']);
                if ($existing && $existing['id'] != $customerId) {
                    return $this->error('用户名已被使用', 409);
                }
            }
            
            $success = $this->customerModel->updateCustomer($customerId, $updateData);
            
            if ($success) {
                return $this->success(null, '更新成功');
            } else {
                return $this->error('更新失败', 500);
            }
            
        } catch (\Exception $e) {
            return $this->error('更新失败: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * 修改密码
     *
     * @return array
     */
    public function changePassword()
    {
        try {
            $customerId = $this->getCurrentUserId();
            $input = $this->getJsonInput();
            
            if (empty($input['current_password']) || empty($input['new_password'])) {
                return $this->error('当前密码和新密码不能为空', 400);
            }
            
            $customer = $this->customerModel->findById($customerId);
            if (!$customer) {
                return $this->error('客户不存在', 404);
            }
            
            // 验证当前密码
            if (!$this->customerModel->verifyPassword($input['current_password'], $customer['password_hash'])) {
                return $this->error('当前密码错误', 401);
            }
            
            // 更新密码
            $success = $this->customerModel->updateCustomer($customerId, [
                'password' => $input['new_password']
            ]);
            
            if ($success) {
                return $this->success(null, '密码修改成功');
            } else {
                return $this->error('密码修改失败', 500);
            }
            
        } catch (\Exception $e) {
            return $this->error('密码修改失败: ' . $e->getMessage(), 500);
        }
    }
}
