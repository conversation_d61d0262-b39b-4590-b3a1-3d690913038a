// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Product _$ProductFromJson(Map<String, dynamic> json) => Product(
  id: (json['id'] as num).toInt(),
  name: json['name'] as String,
  description: json['description'] as String?,
  sku: json['sku'] as String,
  price: (json['price'] as num).toDouble(),
  comparePrice: (json['compare_price'] as num?)?.toDouble(),
  costPrice: (json['cost_price'] as num?)?.toDouble(),
  stockQuantity: (json['stock_quantity'] as num).toInt(),
  lowStockThreshold: (json['low_stock_threshold'] as num?)?.toInt(),
  categoryId: (json['category_id'] as num?)?.toInt(),
  brand: json['brand'] as String?,
  weight: (json['weight'] as num?)?.toDouble(),
  dimensions: json['dimensions'] as String?,
  isFeatured: json['is_featured'] as bool,
  isDigital: json['is_digital'] as bool,
  requiresShipping: json['requires_shipping'] as bool,
  metaTitle: json['meta_title'] as String?,
  metaDescription: json['meta_description'] as String?,
  metaKeywords: json['meta_keywords'] as String?,
  sortOrder: (json['sort_order'] as num).toInt(),
  salesCount: (json['sales_count'] as num).toInt(),
  viewCount: (json['view_count'] as num).toInt(),
  status: $enumDecode(_$ProductStatusEnumMap, json['status']),
  createdAt: DateTime.parse(json['created_at'] as String),
  updatedAt: DateTime.parse(json['updated_at'] as String),
  images: (json['images'] as List<dynamic>?)
      ?.map((e) => ProductImage.fromJson(e as Map<String, dynamic>))
      .toList(),
  variants: (json['variants'] as List<dynamic>?)
      ?.map((e) => ProductVariant.fromJson(e as Map<String, dynamic>))
      .toList(),
  category: json['category'] == null
      ? null
      : Category.fromJson(json['category'] as Map<String, dynamic>),
);

Map<String, dynamic> _$ProductToJson(Product instance) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'description': instance.description,
  'sku': instance.sku,
  'price': instance.price,
  'compare_price': instance.comparePrice,
  'cost_price': instance.costPrice,
  'stock_quantity': instance.stockQuantity,
  'low_stock_threshold': instance.lowStockThreshold,
  'category_id': instance.categoryId,
  'brand': instance.brand,
  'weight': instance.weight,
  'dimensions': instance.dimensions,
  'is_featured': instance.isFeatured,
  'is_digital': instance.isDigital,
  'requires_shipping': instance.requiresShipping,
  'meta_title': instance.metaTitle,
  'meta_description': instance.metaDescription,
  'meta_keywords': instance.metaKeywords,
  'sort_order': instance.sortOrder,
  'sales_count': instance.salesCount,
  'view_count': instance.viewCount,
  'status': _$ProductStatusEnumMap[instance.status]!,
  'created_at': instance.createdAt.toIso8601String(),
  'updated_at': instance.updatedAt.toIso8601String(),
  'images': instance.images,
  'variants': instance.variants,
  'category': instance.category,
};

const _$ProductStatusEnumMap = {
  ProductStatus.active: 'active',
  ProductStatus.inactive: 'inactive',
  ProductStatus.draft: 'draft',
};

ProductImage _$ProductImageFromJson(Map<String, dynamic> json) => ProductImage(
  id: (json['id'] as num).toInt(),
  productId: (json['product_id'] as num).toInt(),
  imageUrl: json['image_url'] as String,
  altText: json['alt_text'] as String?,
  isPrimary: json['is_primary'] as bool,
  sortOrder: (json['sort_order'] as num).toInt(),
);

Map<String, dynamic> _$ProductImageToJson(ProductImage instance) =>
    <String, dynamic>{
      'id': instance.id,
      'product_id': instance.productId,
      'image_url': instance.imageUrl,
      'alt_text': instance.altText,
      'is_primary': instance.isPrimary,
      'sort_order': instance.sortOrder,
    };

ProductVariant _$ProductVariantFromJson(Map<String, dynamic> json) =>
    ProductVariant(
      id: (json['id'] as num).toInt(),
      productId: (json['product_id'] as num).toInt(),
      variantName: json['variant_name'] as String,
      variantValue: json['variant_value'] as String,
      price: (json['price'] as num?)?.toDouble(),
      stockQuantity: (json['stock_quantity'] as num?)?.toInt(),
      sku: json['sku'] as String?,
      sortOrder: (json['sort_order'] as num).toInt(),
    );

Map<String, dynamic> _$ProductVariantToJson(ProductVariant instance) =>
    <String, dynamic>{
      'id': instance.id,
      'product_id': instance.productId,
      'variant_name': instance.variantName,
      'variant_value': instance.variantValue,
      'price': instance.price,
      'stock_quantity': instance.stockQuantity,
      'sku': instance.sku,
      'sort_order': instance.sortOrder,
    };

Category _$CategoryFromJson(Map<String, dynamic> json) => Category(
  id: (json['id'] as num).toInt(),
  name: json['name'] as String,
  description: json['description'] as String?,
  parentId: (json['parent_id'] as num?)?.toInt(),
  imageUrl: json['image_url'] as String?,
  sortOrder: (json['sort_order'] as num).toInt(),
  status: json['status'] as bool,
  createdAt: DateTime.parse(json['created_at'] as String),
  updatedAt: DateTime.parse(json['updated_at'] as String),
  children: (json['children'] as List<dynamic>?)
      ?.map((e) => Category.fromJson(e as Map<String, dynamic>))
      .toList(),
  productCount: (json['product_count'] as num?)?.toInt(),
);

Map<String, dynamic> _$CategoryToJson(Category instance) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'description': instance.description,
  'parent_id': instance.parentId,
  'image_url': instance.imageUrl,
  'sort_order': instance.sortOrder,
  'status': instance.status,
  'created_at': instance.createdAt.toIso8601String(),
  'updated_at': instance.updatedAt.toIso8601String(),
  'children': instance.children,
  'product_count': instance.productCount,
};
