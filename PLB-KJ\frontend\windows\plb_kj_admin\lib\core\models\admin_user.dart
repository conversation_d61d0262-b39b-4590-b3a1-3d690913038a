import 'package:json_annotation/json_annotation.dart';

part 'admin_user.g.dart';

/// 管理员用户模型
@JsonSerializable()
class AdminUser {
  final int id;
  final String username;
  final String email;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'first_name')
  final String? firstName;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'last_name')
  final String? lastName;
  final String? phone;
  final String? avatar;
  final AdminRole role;
  final String? department;
  @JsonKey(name: 'is_online')
  final bool? isOnline;
  @Json<PERSON>ey(name: 'last_login_at')
  final DateTime? lastLoginAt;
  final bool? status;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final DateTime? createdAt;
  @J<PERSON><PERSON>ey(name: 'updated_at')
  final DateTime? updatedAt;

  const AdminUser({
    required this.id,
    required this.username,
    required this.email,
    this.firstName,
    this.lastName,
    this.phone,
    this.avatar,
    required this.role,
    this.department,
    this.isOnline,
    this.lastLoginAt,
    this.status,
    this.createdAt,
    this.updatedAt,
  });

  factory AdminUser.fromJson(Map<String, dynamic> json) =>
      _$AdminUserFromJson(json);

  Map<String, dynamic> toJson() => _$AdminUserToJson(this);

  /// 获取完整姓名
  String get fullName {
    if (firstName != null && lastName != null) {
      return '$firstName $lastName';
    }
    return firstName ?? lastName ?? username;
  }

  /// 获取显示名称
  String get displayName => fullName.isNotEmpty ? fullName : username;

  /// 获取角色显示名称
  String get roleDisplayName {
    switch (role) {
      case AdminRole.admin:
        return '超级管理员';
      case AdminRole.manager:
        return '经理';
      case AdminRole.staff:
        return '员工';
      case AdminRole.customerService:
        return '客服';
    }
  }

  /// 获取状态显示名称
  String get statusDisplayName => (status ?? false) ? '正常' : '禁用';

  /// 获取在线状态显示名称
  String get onlineStatusDisplayName => (isOnline ?? false) ? '在线' : '离线';

  /// 复制并更新部分字段
  AdminUser copyWith({
    int? id,
    String? username,
    String? email,
    String? firstName,
    String? lastName,
    String? phone,
    String? avatar,
    AdminRole? role,
    String? department,
    bool? isOnline,
    DateTime? lastLoginAt,
    bool? status,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AdminUser(
      id: id ?? this.id,
      username: username ?? this.username,
      email: email ?? this.email,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      phone: phone ?? this.phone,
      avatar: avatar ?? this.avatar,
      role: role ?? this.role,
      department: department ?? this.department,
      isOnline: isOnline ?? this.isOnline,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// 管理员角色枚举
@JsonEnum()
enum AdminRole {
  @JsonValue('admin')
  admin,
  @JsonValue('manager')
  manager,
  @JsonValue('staff')
  staff,
  @JsonValue('customer_service')
  customerService,
}

/// 管理员登录请求模型
@JsonSerializable()
class AdminLoginRequest {
  final String username;
  final String password;
  final String? captcha;

  const AdminLoginRequest({
    required this.username,
    required this.password,
    this.captcha,
  });

  factory AdminLoginRequest.fromJson(Map<String, dynamic> json) =>
      _$AdminLoginRequestFromJson(json);

  Map<String, dynamic> toJson() => _$AdminLoginRequestToJson(this);
}

/// 管理员登录响应模型
@JsonSerializable()
class AdminLoginResponse {
  final String token;
  @JsonKey(name: 'user')
  final AdminUser admin;

  const AdminLoginResponse({
    required this.token,
    required this.admin,
  });

  factory AdminLoginResponse.fromJson(Map<String, dynamic> json) =>
      _$AdminLoginResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AdminLoginResponseToJson(this);
}

/// 管理员创建/更新请求模型
@JsonSerializable()
class AdminUserRequest {
  final String username;
  final String email;
  final String? password;
  @JsonKey(name: 'first_name')
  final String? firstName;
  @JsonKey(name: 'last_name')
  final String? lastName;
  final String? phone;
  final String? avatar;
  final AdminRole role;
  final String? department;
  final bool? status;

  const AdminUserRequest({
    required this.username,
    required this.email,
    this.password,
    this.firstName,
    this.lastName,
    this.phone,
    this.avatar,
    required this.role,
    this.department,
    this.status,
  });

  factory AdminUserRequest.fromJson(Map<String, dynamic> json) =>
      _$AdminUserRequestFromJson(json);

  Map<String, dynamic> toJson() => _$AdminUserRequestToJson(this);
}
