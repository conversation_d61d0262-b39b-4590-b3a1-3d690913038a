<?php

namespace App\Models;

class Product extends BaseModel
{
    protected $table = 'products'; // 对应 plb_kj_products 表
    
    /**
     * 根据分类获取商品
     *
     * @param int $categoryId
     * @param int $page
     * @param int $pageSize
     * @return array
     */
    public function findByCategory($categoryId, $page = 1, $pageSize = 20)
    {
        $conditions = ['category_id = ?'];
        $params = [$categoryId];
        
        return $this->findAllWithPagination($page, $pageSize, $conditions, $params);
    }
    
    /**
     * 搜索商品
     *
     * @param string $keyword
     * @param int $page
     * @param int $pageSize
     * @return array
     */
    public function search($keyword, $page = 1, $pageSize = 20)
    {
        $conditions = ['(name LIKE ? OR description LIKE ?)'];
        $params = ["%$keyword%", "%$keyword%"];
        
        return $this->findAllWithPagination($page, $pageSize, $conditions, $params);
    }
    
    /**
     * 获取商品详情（包含图片和变体）
     *
     * @param int $id
     * @return array|null
     */
    public function getProductDetails($id)
    {
        $product = $this->findById($id);
        if (!$product) {
            return null;
        }
        
        // 获取商品图片
        $imagesSql = "SELECT * FROM {$this->prefix}product_images WHERE product_id = ? ORDER BY sort_order ASC";
        $stmt = $this->db->prepare($imagesSql);
        $stmt->execute([$id]);
        $product['images'] = $stmt->fetchAll(\PDO::FETCH_ASSOC);
        
        // 获取商品变体
        $variantsSql = "SELECT * FROM {$this->prefix}product_variants WHERE product_id = ? ORDER BY sort_order ASC";
        $stmt = $this->db->prepare($variantsSql);
        $stmt->execute([$id]);
        $product['variants'] = $stmt->fetchAll(\PDO::FETCH_ASSOC);
        
        return $product;
    }
    
    /**
     * 更新商品库存
     *
     * @param int $id
     * @param int $quantity
     * @return bool
     */
    public function updateStock($id, $quantity)
    {
        return $this->update($id, ['stock_quantity' => $quantity]);
    }
    
    /**
     * 减少库存
     *
     * @param int $id
     * @param int $quantity
     * @return bool
     */
    public function decreaseStock($id, $quantity)
    {
        $sql = "UPDATE {$this->table} SET stock_quantity = stock_quantity - ? WHERE id = ? AND stock_quantity >= ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$quantity, $id, $quantity]);
        
        return $stmt->rowCount() > 0;
    }
    
    /**
     * 增加库存
     *
     * @param int $id
     * @param int $quantity
     * @return bool
     */
    public function increaseStock($id, $quantity)
    {
        $sql = "UPDATE {$this->table} SET stock_quantity = stock_quantity + ? WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$quantity, $id]);
        
        return $stmt->rowCount() > 0;
    }
    
    /**
     * 获取热销商品
     *
     * @param int $limit
     * @return array
     */
    public function getPopularProducts($limit = 10)
    {
        $sql = "SELECT * FROM {$this->table} WHERE status = 'active' ORDER BY sales_count DESC LIMIT ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$limit]);
        
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }
    
    /**
     * 获取新品
     *
     * @param int $limit
     * @return array
     */
    public function getNewProducts($limit = 10)
    {
        $sql = "SELECT * FROM {$this->table} WHERE status = 'active' ORDER BY created_at DESC LIMIT ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$limit]);
        
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }
    
    /**
     * 获取推荐商品
     *
     * @param int $limit
     * @return array
     */
    public function getFeaturedProducts($limit = 10)
    {
        $sql = "SELECT * FROM {$this->table} WHERE status = 'active' AND is_featured = 1 ORDER BY sort_order ASC LIMIT ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$limit]);
        
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }
}
