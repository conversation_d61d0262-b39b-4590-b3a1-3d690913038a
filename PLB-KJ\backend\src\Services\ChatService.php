<?php

namespace App\Services;

use App\Models\ChatSession;
use App\Models\ChatMessage;
use App\Helpers\Database;

class ChatService
{
    private $chatSession;
    private $chatMessage;
    private $db;

    public function __construct()
    {
        $this->chatSession = new ChatSession();
        $this->chatMessage = new ChatMessage();
        $this->db = Database::getInstance();
    }

    /**
     * 创建新的聊天会话
     */
    public function createSession($customerId, $title = '客服咨询', $category = 'general', $priority = 'normal')
    {
        // 检查是否已有活跃会话
        $activeSession = $this->chatSession->getActiveSessionByCustomer($customerId);
        if ($activeSession) {
            throw new \Exception('您已有活跃的会话，请先结束当前会话');
        }

        // 生成会话ID
        $sessionId = $this->generateUUID();

        // 创建会话数据
        $sessionData = [
            'session_id' => $sessionId,
            'customer_id' => $customerId,
            'title' => $title,
            'category' => $category,
            'priority' => $priority
        ];

        // 创建会话
        $newSessionId = $this->chatSession->create($sessionData);

        // 尝试自动分配客服
        $assignedAgent = $this->chatSession->autoAssignAgent($newSessionId);

        // 返回会话信息
        return $this->chatSession->findById($newSessionId);
    }

    /**
     * 发送消息
     */
    public function sendMessage($sessionId, $senderId, $senderType, $content, $messageType = 'text', $replyToMessageId = null)
    {
        // 验证会话权限
        if (!$this->chatSession->hasAccess($sessionId, $senderId, $senderType)) {
            throw new \Exception('无权访问此会话');
        }

        // 创建消息数据
        $messageData = [
            'session_id' => $sessionId,
            'sender_type' => $senderType,
            'sender_id' => $senderId,
            'message_type' => $messageType,
            'content' => $content,
            'reply_to_message_id' => $replyToMessageId
        ];

        // 创建消息
        $messageId = $this->chatMessage->create($messageData);

        // 更新会话最后消息时间
        $this->chatSession->updateLastMessageTime($sessionId);

        // 返回消息信息
        return $this->chatMessage->findById($messageId);
    }

    /**
     * 接受会话（管理员）
     */
    public function acceptSession($sessionId, $adminUserId)
    {
        $session = $this->chatSession->findById($sessionId);
        if (!$session) {
            throw new \Exception('会话不存在');
        }

        if ($session['status'] !== 'waiting') {
            throw new \Exception('会话状态不正确');
        }

        // 更新会话状态
        $result = $this->chatSession->updateStatus($sessionId, 'active', $adminUserId);
        if ($result === 0) {
            throw new \Exception('接受会话失败');
        }

        return $this->chatSession->findById($sessionId);
    }

    /**
     * 关闭会话
     */
    public function closeSession($sessionId, $userId, $userType, $rating = null, $feedback = null)
    {
        // 验证会话权限
        if (!$this->chatSession->hasAccess($sessionId, $userId, $userType)) {
            throw new \Exception('无权访问此会话');
        }

        $session = $this->chatSession->findById($sessionId);
        if (!$session) {
            throw new \Exception('会话不存在');
        }

        if ($session['status'] === 'closed') {
            throw new \Exception('会话已关闭');
        }

        // 关闭会话
        $result = $this->chatSession->close($sessionId, $rating, $feedback);
        if ($result === 0) {
            throw new \Exception('关闭会话失败');
        }

        return $this->chatSession->findById($sessionId);
    }

    /**
     * 标记消息已读
     */
    public function markMessagesRead($sessionId, $messageIds, $userId, $userType)
    {
        // 验证会话权限
        if (!$this->chatSession->hasAccess($sessionId, $userId, $userType)) {
            throw new \Exception('无权访问此会话');
        }

        if (empty($messageIds)) {
            return 0;
        }

        return $this->chatMessage->markAsRead($messageIds, $sessionId);
    }

    /**
     * 获取会话列表
     */
    public function getUserSessions($userId, $userType)
    {
        return $this->chatSession->findByUser($userId, $userType);
    }

    /**
     * 获取会话消息
     */
    public function getSessionMessages($sessionId, $userId, $userType, $limit = 50, $offset = 0)
    {
        // 验证会话权限
        if (!$this->chatSession->hasAccess($sessionId, $userId, $userType)) {
            throw new \Exception('无权访问此会话');
        }

        return $this->chatMessage->findBySession($sessionId, $limit, $offset);
    }

    /**
     * 获取聊天统计
     */
    public function getChatStats($date = null)
    {
        $sessionStats = $this->chatSession->getStats($date);
        $messageStats = $this->chatMessage->getStats($date);

        return array_merge($sessionStats, $messageStats);
    }

    /**
     * 搜索消息
     */
    public function searchMessages($sessionId, $keyword, $userId, $userType, $limit = 20, $offset = 0)
    {
        // 验证会话权限
        if (!$this->chatSession->hasAccess($sessionId, $userId, $userType)) {
            throw new \Exception('无权访问此会话');
        }

        return $this->chatMessage->search($sessionId, $keyword, $limit, $offset);
    }

    /**
     * 获取等待中的会话（管理员）
     */
    public function getWaitingSessions()
    {
        return $this->chatSession->getWaitingSessions();
    }

    /**
     * 更新用户在线状态
     */
    public function updateOnlineStatus($userId, $userType, $isOnline)
    {
        $tableName = $userType === 'customer' ? 'plb_kj_customers' : 'plb_kj_admin_users';
        
        $sql = "UPDATE {$tableName} SET is_online = ?, last_login_at = NOW() WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([$isOnline ? 1 : 0, $userId]);

        return $stmt->rowCount();
    }

    /**
     * 获取在线用户列表
     */
    public function getOnlineUsers($userType = null)
    {
        if ($userType === 'customer') {
            $sql = "SELECT id, first_name, last_name, email, avatar, last_login_at 
                    FROM plb_kj_customers WHERE is_online = 1";
        } elseif ($userType === 'admin') {
            $sql = "SELECT id, first_name, last_name, email, avatar, role, last_login_at 
                    FROM plb_kj_admin_users WHERE is_online = 1";
        } else {
            $sql = "SELECT 'customer' as user_type, id, first_name, last_name, email, avatar, last_login_at 
                    FROM plb_kj_customers WHERE is_online = 1
                    UNION ALL
                    SELECT 'admin' as user_type, id, first_name, last_name, email, avatar, last_login_at 
                    FROM plb_kj_admin_users WHERE is_online = 1";
        }

        $stmt = $this->db->query($sql);
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }

    /**
     * 获取用户信息
     */
    public function getUserInfo($userId, $userType)
    {
        if ($userType === 'customer') {
            $sql = "SELECT id, first_name, last_name, email, avatar, is_online 
                    FROM plb_kj_customers WHERE id = ?";
        } else {
            $sql = "SELECT id, first_name, last_name, email, avatar, role, is_online 
                    FROM plb_kj_admin_users WHERE id = ?";
        }

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$userId]);
        
        return $stmt->fetch(\PDO::FETCH_ASSOC);
    }

    /**
     * 生成UUID
     */
    private function generateUUID()
    {
        return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }

    /**
     * 格式化会话数据
     */
    public function formatSessionData($session)
    {
        return [
            'id' => (int)$session['id'],
            'session_id' => $session['session_id'],
            'customer_id' => (int)$session['customer_id'],
            'admin_user_id' => $session['admin_user_id'] ? (int)$session['admin_user_id'] : null,
            'title' => $session['title'],
            'status' => $session['status'],
            'priority' => $session['priority'],
            'category' => $session['category'],
            'started_at' => $session['started_at'],
            'ended_at' => $session['ended_at'],
            'last_message_at' => $session['last_message_at'],
            'customer_satisfaction_rating' => $session['customer_satisfaction_rating'] ? (int)$session['customer_satisfaction_rating'] : null,
            'customer_feedback' => $session['customer_feedback'],
            'unread_count' => isset($session['unread_count']) ? (int)$session['unread_count'] : 0,
            'customer' => [
                'id' => (int)$session['customer_id'],
                'first_name' => $session['customer_first_name'],
                'last_name' => $session['customer_last_name'],
                'email' => $session['customer_email'],
                'avatar' => $session['customer_avatar'],
                'is_online' => (bool)$session['customer_is_online']
            ],
            'admin' => $session['admin_user_id'] ? [
                'id' => (int)$session['admin_user_id'],
                'first_name' => $session['admin_first_name'],
                'last_name' => $session['admin_last_name'],
                'email' => $session['admin_email'],
                'avatar' => $session['admin_avatar'],
                'is_online' => (bool)$session['admin_is_online']
            ] : null
        ];
    }

    /**
     * 格式化消息数据
     */
    public function formatMessageData($message)
    {
        return [
            'id' => (int)$message['id'],
            'session_id' => (int)$message['session_id'],
            'sender_type' => $message['sender_type'],
            'sender_id' => (int)$message['sender_id'],
            'sender_name' => trim($message['sender_first_name'] . ' ' . $message['sender_last_name']),
            'sender_avatar' => $message['sender_avatar'],
            'message_type' => $message['message_type'],
            'content' => $message['content'],
            'reply_to_message_id' => $message['reply_to_message_id'] ? (int)$message['reply_to_message_id'] : null,
            'is_read' => (bool)$message['is_read'],
            'created_at' => $message['created_at'],
            'read_at' => $message['read_at']
        ];
    }
}
