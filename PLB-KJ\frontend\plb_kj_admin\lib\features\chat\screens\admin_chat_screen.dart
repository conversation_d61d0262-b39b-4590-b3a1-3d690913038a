import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../services/admin_chat_service.dart';
import '../models/chat_models.dart';

class AdminChatScreen extends StatefulWidget {
  const AdminChatScreen({Key? key}) : super(key: key);

  @override
  State<AdminChatScreen> createState() => _AdminChatScreenState();
}

class _AdminChatScreenState extends State<AdminChatScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  ChatSession? _selectedSession;
  final TextEditingController _messageController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    
    // 初始化聊天服务
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AdminChatService>().initialize();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _messageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('客服聊天管理'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(text: '全部会话', icon: Icon(Icons.chat_bubble_outline)),
            Tab(text: '等待中', icon: Icon(Icons.schedule)),
            Tab(text: '进行中', icon: Icon(Icons.chat)),
            Tab(text: '已结束', icon: Icon(Icons.check_circle_outline)),
          ],
        ),
        actions: [
          Consumer<AdminChatService>(
            builder: (context, chatService, child) {
              return Container(
                margin: const EdgeInsets.only(right: 16),
                child: Row(
                  children: [
                    Icon(
                      chatService.isConnected 
                          ? Icons.cloud_done 
                          : Icons.cloud_off,
                      color: chatService.isConnected 
                          ? Colors.green 
                          : Colors.red,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      chatService.isConnected ? '已连接' : '未连接',
                      style: const TextStyle(fontSize: 12),
                    ),
                  ],
                ),
              );
            },
          ),
          IconButton(
            onPressed: () {
              context.read<AdminChatService>().refresh();
            },
            icon: const Icon(Icons.refresh),
            tooltip: '刷新',
          ),
        ],
      ),
      body: Row(
        children: [
          // 左侧会话列表
          Expanded(
            flex: 1,
            child: Column(
              children: [
                // 统计信息
                Consumer<AdminChatService>(
                  builder: (context, chatService, child) {
                    return Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        border: Border(
                          bottom: BorderSide(color: Colors.grey[300]!),
                        ),
                      ),
                      child: Row(
                        children: [
                          _buildStatCard('总会话', chatService.sessions.length.toString(), Colors.blue),
                          const SizedBox(width: 12),
                          _buildStatCard('等待中', chatService.waitingSessions.length.toString(), Colors.orange),
                          const SizedBox(width: 12),
                          _buildStatCard('进行中', chatService.activeSessions.length.toString(), Colors.green),
                        ],
                      ),
                    );
                  },
                ),
                
                // 会话列表
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildSessionList('all'),
                      _buildSessionList('waiting'),
                      _buildSessionList('active'),
                      _buildSessionList('closed'),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          // 右侧聊天区域
          Expanded(
            flex: 2,
            child: _selectedSession != null 
                ? _buildChatArea(_selectedSession!)
                : _buildEmptyChatArea(),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            Text(
              value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[700],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSessionList(String filter) {
    return Consumer<AdminChatService>(
      builder: (context, chatService, child) {
        List<ChatSession> filteredSessions;
        
        switch (filter) {
          case 'waiting':
            filteredSessions = chatService.waitingSessions;
            break;
          case 'active':
            filteredSessions = chatService.activeSessions;
            break;
          case 'closed':
            filteredSessions = chatService.closedSessions;
            break;
          default:
            filteredSessions = chatService.sessions;
        }

        if (filteredSessions.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.chat_bubble_outline,
                  size: 64,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Text(
                  _getEmptyMessage(filter),
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(8),
          itemCount: filteredSessions.length,
          itemBuilder: (context, index) {
            final session = filteredSessions[index];
            return _buildSessionItem(session);
          },
        );
      },
    );
  }

  Widget _buildSessionItem(ChatSession session) {
    final isSelected = _selectedSession?.id == session.id;
    
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      color: isSelected ? Colors.blue[50] : null,
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Colors.blue[100],
          child: Text(
            session.customer?.fullName.substring(0, 1).toUpperCase() ?? 'C',
            style: TextStyle(
              color: Colors.blue[800],
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          session.customer?.fullName ?? '客户 ${session.customerId}',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(session.title),
            const SizedBox(height: 4),
            Row(
              children: [
                _buildStatusChip(session.status),
                const SizedBox(width: 8),
                Text(
                  DateFormat('MM-dd HH:mm').format(session.lastMessageAt),
                  style: const TextStyle(fontSize: 12),
                ),
              ],
            ),
          ],
        ),
        trailing: session.unreadCount > 0
            ? Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${session.unreadCount}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              )
            : null,
        onTap: () => _selectSession(session),
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color color;
    String text;
    
    switch (status) {
      case 'waiting':
        color = Colors.orange;
        text = '等待中';
        break;
      case 'active':
        color = Colors.green;
        text = '进行中';
        break;
      case 'closed':
        color = Colors.grey;
        text = '已结束';
        break;
      default:
        color = Colors.grey;
        text = '未知';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 10,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildEmptyChatArea() {
    return Container(
      decoration: BoxDecoration(
        border: Border(left: BorderSide(color: Colors.grey[300]!)),
      ),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.chat_bubble_outline,
              size: 80,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              '选择一个会话开始聊天',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChatArea(ChatSession session) {
    return Container(
      decoration: BoxDecoration(
        border: Border(left: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Column(
        children: [
          // 聊天头部
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border(bottom: BorderSide(color: Colors.grey[300]!)),
            ),
            child: Row(
              children: [
                CircleAvatar(
                  backgroundColor: Colors.blue[100],
                  child: Text(
                    session.customer?.fullName.substring(0, 1).toUpperCase() ?? 'C',
                    style: TextStyle(
                      color: Colors.blue[800],
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        session.customer?.fullName ?? '客户 ${session.customerId}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        session.title,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                if (session.status == 'waiting')
                  ElevatedButton.icon(
                    onPressed: () => _acceptSession(session),
                    icon: const Icon(Icons.check, size: 16),
                    label: const Text('接受'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                if (session.status == 'active')
                  ElevatedButton.icon(
                    onPressed: () => _closeSession(session),
                    icon: const Icon(Icons.close, size: 16),
                    label: const Text('结束'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  ),
              ],
            ),
          ),
          
          // 消息列表
          Expanded(
            child: _buildMessageList(session),
          ),
          
          // 消息输入框
          if (session.status == 'active')
            _buildMessageInput(session),
        ],
      ),
    );
  }

  Widget _buildMessageList(ChatSession session) {
    return Consumer<AdminChatService>(
      builder: (context, chatService, child) {
        final messages = chatService.messages[session.id.toString()] ?? [];
        
        if (messages.isEmpty) {
          // 自动加载消息
          WidgetsBinding.instance.addPostFrameCallback((_) {
            chatService.getSessionMessages(session.id);
          });
          
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: messages.length,
          itemBuilder: (context, index) {
            final message = messages[index];
            final isMe = message.senderType == 'admin';
            
            return Container(
              margin: const EdgeInsets.symmetric(vertical: 4),
              child: Row(
                mainAxisAlignment: isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
                children: [
                  if (!isMe) ...[
                    CircleAvatar(
                      radius: 16,
                      backgroundColor: Colors.grey[300],
                      child: Text(
                        message.senderName.substring(0, 1).toUpperCase(),
                        style: const TextStyle(fontSize: 12),
                      ),
                    ),
                    const SizedBox(width: 8),
                  ],
                  Flexible(
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                      decoration: BoxDecoration(
                        color: isMe ? Colors.blue[600] : Colors.grey[200],
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            message.content,
                            style: TextStyle(
                              color: isMe ? Colors.white : Colors.black87,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            DateFormat('HH:mm').format(message.createdAt),
                            style: TextStyle(
                              fontSize: 10,
                              color: isMe ? Colors.white70 : Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  if (isMe) ...[
                    const SizedBox(width: 8),
                    CircleAvatar(
                      radius: 16,
                      backgroundColor: Colors.blue[100],
                      child: Text(
                        message.senderName.substring(0, 1).toUpperCase(),
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.blue[800],
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildMessageInput(ChatSession session) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _messageController,
              decoration: InputDecoration(
                hintText: '输入消息...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(24),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
              ),
              maxLines: 4,
              minLines: 1,
              onSubmitted: (_) => _sendMessage(session),
            ),
          ),
          const SizedBox(width: 8),
          Container(
            decoration: BoxDecoration(
              color: Colors.blue[600],
              shape: BoxShape.circle,
            ),
            child: IconButton(
              onPressed: () => _sendMessage(session),
              icon: const Icon(
                Icons.send,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getEmptyMessage(String filter) {
    switch (filter) {
      case 'waiting':
        return '暂无等待中的会话';
      case 'active':
        return '暂无进行中的会话';
      case 'closed':
        return '暂无已结束的会话';
      default:
        return '暂无会话';
    }
  }

  void _selectSession(ChatSession session) {
    setState(() {
      _selectedSession = session;
    });
    
    // 加载消息
    context.read<AdminChatService>().getSessionMessages(session.id);
  }

  void _sendMessage(ChatSession session) async {
    final content = _messageController.text.trim();
    if (content.isEmpty) return;

    try {
      await context.read<AdminChatService>().sendMessage(
        sessionId: session.id,
        content: content,
      );
      _messageController.clear();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('发送消息失败: $e')),
      );
    }
  }

  void _acceptSession(ChatSession session) async {
    try {
      final success = await context.read<AdminChatService>().acceptSession(session.id);
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('会话已接受')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('接受会话失败: $e')),
      );
    }
  }

  void _closeSession(ChatSession session) async {
    try {
      final success = await context.read<AdminChatService>().closeSession(
        sessionId: session.id,
      );
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('会话已结束')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('结束会话失败: $e')),
      );
    }
  }
}
